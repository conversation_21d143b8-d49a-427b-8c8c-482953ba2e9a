import React from 'react';
import { motion } from 'framer-motion';
import { GradientBuilder } from '@/components/gradient-builder/GradientBuilder';
import { GradientState } from '@/types/types';
import Footer from '@/components/layouts/Footer';

const GradientBuilderPage: React.FC = () => {
  // Handle saving gradients (could integrate with localStorage or API)
  const handleSaveGradient = async (gradient: GradientState) => {
    try {
      // Save to localStorage for now
      const savedGradients = JSON.parse(localStorage.getItem('savedGradients') || '[]');
      const updatedGradients = [...savedGradients, gradient];
      localStorage.setItem('savedGradients', JSON.stringify(updatedGradients));
      
      console.log('Gradient saved:', gradient);
    } catch (error) {
      console.error('Failed to save gradient:', error);
      throw error;
    }
  };

  // Handle exporting gradients
  const handleExportGradient = (gradient: GradientState, format: string) => {
    console.log('Exporting gradient:', gradient, 'in format:', format);
    // Could implement additional export functionality here
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-background"
    >
      {/* Main Content */}
      <GradientBuilder
        onSave={handleSaveGradient}
        onExport={handleExportGradient}
      />
      
      {/* Footer */}
      <Footer />
    </motion.div>
  );
};

export default GradientBuilderPage;
