import React, { useState, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Save, RotateCcw, Share2, Download, History, Undo, Redo } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MagicCard } from '@/components/ui/MagicCard';
import { useTheme } from 'next-themes';
import { GradientBuilderProps } from '@/types/types';
import { useGradient } from '@/hooks/useGradient';
import { useCustomToast } from '@/hooks/useCustomToast';

// Import gradient builder components
import { GradientTypeSelector } from './GradientTypeSelector';
import { ColorStopManager } from './ColorStopManager';
import { GradientControls } from './GradientControls';
import { GradientPreview } from './GradientPreview';
import { CodeOutput } from './CodeOutput';
import { GradientPresets } from './GradientPresets';
import { GradientHistory } from './GradientHistory';
import { GradientPreset } from '@/data/gradientPresets';
import { useGradientHistory } from '@/hooks/useGradientHistory';

export const GradientBuilder: React.FC<GradientBuilderProps> = ({
  initialGradient,
  onSave,
  onExport
}) => {
  const [activeCodeFormat, setActiveCodeFormat] = useState<'css' | 'tailwind' | 'sass' | 'bootstrap'>('css');
  const [isSaving, setIsSaving] = useState(false);
  const [showPresets, setShowPresets] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const { showToast } = useCustomToast();
  const { theme } = useTheme();
  const { addToHistory } = useGradientHistory();

  // Use the gradient hook for state management
  const {
    gradient,
    sortedColorStops,
    updateName,
    updateConfig,
    updateGradientType,
    addColorStop,
    removeColorStop,
    updateColorStop,
    updateColorStopColor,
    updateColorStopPosition,
    updateColorStopAlpha,
    reorderColorStops,
    resetGradient,
    generatedCode,
    cssGradient,
    previewStyle,
    canRemoveStop,
    canAddStop,
    canUndo,
    canRedo,
    undo,
    redo
  } = useGradient(initialGradient);

  // Auto-save to history with debouncing
  const lastSavedRef = useRef<string>('');
  const saveTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Create a signature of the current gradient state
    const currentSignature = JSON.stringify({
      name: gradient.name,
      config: gradient.config,
      colorStops: gradient.colorStops.map(stop => ({
        color: stop.color,
        position: stop.position,
        alpha: stop.alpha
      }))
    });

    // Only save if the gradient has actually changed
    if (currentSignature !== lastSavedRef.current) {
      saveTimeoutRef.current = setTimeout(() => {
        addToHistory(gradient);
        lastSavedRef.current = currentSignature;
      }, 2000); // Save after 2 seconds of inactivity
    }

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [gradient, addToHistory]);

  // Handle gradient name change
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateName(e.target.value);
  }, [updateName]);

  // Handle gradient type change
  const handleGradientTypeChange = useCallback((type: 'linear' | 'radial' | 'conic') => {
    updateGradientType(type);
  }, [updateGradientType]);

  // Handle color stops change
  const handleColorStopsChange = useCallback((colorStops: any[]) => {
    // This is handled by individual color stop updates
    // The ColorStopManager will call updateColorStop, addColorStop, removeColorStop directly
  }, []);

  // Create handlers for ColorStopManager
  const colorStopHandlers = {
    onAdd: addColorStop,
    onRemove: removeColorStop,
    onUpdate: updateColorStop,
    onUpdateColor: updateColorStopColor,
    onUpdatePosition: updateColorStopPosition,
    onUpdateAlpha: updateColorStopAlpha,
    onReorder: reorderColorStops
  };

  // Handle save functionality
  const handleSave = useCallback(async () => {
    if (!onSave) return;
    
    setIsSaving(true);
    try {
      await onSave(gradient);
      showToast('Gradient saved successfully!', 'success');
    } catch (error) {
      showToast('Failed to save gradient', 'error');
    } finally {
      setIsSaving(false);
    }
  }, [gradient, onSave, showToast]);

  // Handle history selection
  const handleHistorySelect = useCallback((selectedGradient: GradientState) => {
    // Update the gradient state with the selected gradient
    updateName(selectedGradient.name);
    updateConfig(selectedGradient.config);

    // Clear existing color stops and add new ones
    const currentStopIds = gradient.colorStops.map(stop => stop.id);
    currentStopIds.forEach(id => removeColorStop(id));

    // Add color stops from selected gradient
    selectedGradient.colorStops.forEach(() => {
      addColorStop();
    });

    // Update the color stops with the correct data
    setTimeout(() => {
      selectedGradient.colorStops.forEach((stop, index) => {
        if (gradient.colorStops[index]) {
          updateColorStop(gradient.colorStops[index].id, {
            color: stop.color,
            position: stop.position,
            alpha: stop.alpha,
            format: stop.format
          });
        }
      });
    }, 100);

    setShowHistory(false);
    showToast(`Loaded gradient: ${selectedGradient.name}`, 'success');
  }, [gradient.colorStops, updateName, updateConfig, removeColorStop, addColorStop, updateColorStop, showToast]);

  // Handle export functionality
  const handleExport = useCallback((code: string, format: string) => {
    if (onExport) {
      onExport(gradient, format);
    }
  }, [gradient, onExport]);

  // Handle reset functionality
  const handleReset = useCallback(() => {
    resetGradient();
    showToast('Gradient reset to default', 'info');
  }, [resetGradient, showToast]);

  // Handle preset selection
  const handlePresetSelect = useCallback((preset: GradientPreset) => {
    // Create a new gradient state from the preset
    const newGradient = {
      ...preset.gradient,
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Update the gradient state
    updateName(newGradient.name);
    updateConfig(newGradient.config);

    // Update color stops
    newGradient.colorStops.forEach((stop, index) => {
      if (index < gradient.colorStops.length) {
        updateColorStop(gradient.colorStops[index].id, stop);
      } else {
        addColorStop();
      }
    });

    // Remove extra color stops if needed
    if (gradient.colorStops.length > newGradient.colorStops.length) {
      const stopsToRemove = gradient.colorStops.slice(newGradient.colorStops.length);
      stopsToRemove.forEach(stop => removeColorStop(stop.id));
    }

    setShowPresets(false);
    showToast(`Applied preset: ${preset.name}`, 'success');
  }, [gradient.colorStops, updateName, updateConfig, updateColorStop, addColorStop, removeColorStop, showToast]);

  // Handle share functionality
  const handleShare = useCallback(async () => {
    try {
      const shareData = {
        title: `${gradient.name} - CSS Gradient`,
        text: `Check out this gradient: ${gradient.name}`,
        url: window.location.href
      };

      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy URL to clipboard
        await navigator.clipboard.writeText(window.location.href);
        showToast('Gradient URL copied to clipboard!', 'success');
      }
    } catch (error) {
      showToast('Failed to share gradient', 'error');
    }
  }, [gradient.name, showToast]);

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-author font-bold text-[var(--headline)] tracking-tight">
                Gradient Builder
              </h1>
              <p className="text-[var(--paragraph)] font-author font-normal">
                Create beautiful CSS gradients with an intuitive interface
              </p>
            </div>
            
            <div className="flex items-center gap-2 flex-wrap">
              {/* Undo/Redo buttons */}
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  onClick={undo}
                  disabled={!canUndo}
                  size="sm"
                  title="Undo (Ctrl+Z)"
                >
                  <Undo className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  onClick={redo}
                  disabled={!canRedo}
                  size="sm"
                  title="Redo (Ctrl+Y)"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </div>

              <Button
                variant="outline"
                onClick={() => setShowPresets(!showPresets)}
                size="sm"
                className={showPresets ? 'bg-[var(--active)] text-[var(--active-text)]' : ''}
              >
                <Download className="h-4 w-4 mr-2" />
                Presets
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowHistory(!showHistory)}
                size="sm"
                className={showHistory ? 'bg-[var(--active)] text-[var(--active-text)]' : ''}
              >
                <History className="h-4 w-4 mr-2" />
                History
              </Button>

              <Button
                variant="outline"
                onClick={handleReset}
                size="sm"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>

              <Button
                variant="outline"
                onClick={handleShare}
                size="sm"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>

              {onSave && (
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  size="sm"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save'}
                </Button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Presets Panel */}
        {showPresets && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-8"
          >
            <MagicCard
              gradientColor={theme === "dark" ? "#262626" : "#282828"}
              className="p-6"
            >
              <GradientPresets onSelectPreset={handlePresetSelect} />
            </MagicCard>
          </motion.div>
        )}

        {/* History Panel */}
        {showHistory && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-8"
          >
            <MagicCard
              gradientColor={theme === "dark" ? "#262626" : "#282828"}
              className="p-6"
            >
              <GradientHistory onSelectGradient={handleHistorySelect} />
            </MagicCard>
          </motion.div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Controls */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <MagicCard
              gradientColor={theme === "dark" ? "#262626" : "#282828"}
              className="p-6 space-y-6"
            >
              {/* Gradient Name */}
              <div className="space-y-2">
                <Label htmlFor="gradient-name" className="text-lg font-semibold text-[var(--headline)]">
                  Gradient Name
                </Label>
                <Input
                  id="gradient-name"
                  value={gradient.name}
                  onChange={handleNameChange}
                  placeholder="Enter gradient name..."
                  className="text-lg bg-[var(--input-background)] border-[var(--input-border-color)] text-[var(--input-text)]"
                />
              </div>

              <Separator />

              {/* Gradient Type Selector */}
              <GradientTypeSelector
                gradientType={gradient.config.type}
                onGradientTypeChange={handleGradientTypeChange}
                showIcons={true}
              />

              <Separator />

              {/* Gradient Controls */}
              <GradientControls
                config={gradient.config}
                onConfigChange={updateConfig}
              />

              <Separator />

              {/* Color Stop Manager */}
              <ScrollArea className="h-96">
                <ColorStopManager
                  colorStops={gradient.colorStops}
                  onColorStopsChange={handleColorStopsChange}
                  onAddColorStop={colorStopHandlers.onAdd}
                  onRemoveColorStop={colorStopHandlers.onRemove}
                  onUpdateColorStop={colorStopHandlers.onUpdate}
                  maxStops={10}
                  minStops={2}
                />
              </ScrollArea>
            </MagicCard>
          </motion.div>

          {/* Right Panel - Preview and Code */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <MagicCard
              gradientColor={theme === "dark" ? "#262626" : "#282828"}
              className="p-6 space-y-6"
            >
              {/* Gradient Preview */}
              <GradientPreview
                gradient={gradient}
                backgroundPattern="transparent"
              />

              <Separator />

              {/* Code Output */}
              <CodeOutput
                gradient={gradient}
                activeFormat={activeCodeFormat}
                onFormatChange={setActiveCodeFormat}
                onCopy={handleExport}
              />
            </MagicCard>
          </motion.div>
        </div>

        {/* Mobile-friendly bottom actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 lg:hidden"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              variant="outline"
              onClick={handleReset}
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            
            <Button
              variant="outline"
              onClick={handleShare}
              size="sm"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            
            {onSave && (
              <Button
                onClick={handleSave}
                disabled={isSaving}
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
