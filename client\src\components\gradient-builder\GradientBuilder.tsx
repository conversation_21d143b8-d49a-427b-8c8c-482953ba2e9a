import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Save, RotateCcw, Share2, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { GradientBuilderProps } from '@/types/types';
import { useGradient } from '@/hooks/useGradient';
import { useCustomToast } from '@/hooks/useCustomToast';

// Import gradient builder components
import { GradientTypeSelector } from './GradientTypeSelector';
import { ColorStopManager } from './ColorStopManager';
import { GradientControls } from './GradientControls';
import { GradientPreview } from './GradientPreview';
import { CodeOutput } from './CodeOutput';

export const GradientBuilder: React.FC<GradientBuilderProps> = ({
  initialGradient,
  onSave,
  onExport
}) => {
  const [activeCodeFormat, setActiveCodeFormat] = useState<'css' | 'tailwind' | 'sass' | 'bootstrap'>('css');
  const [isSaving, setIsSaving] = useState(false);
  const { showToast } = useCustomToast();

  // Use the gradient hook for state management
  const {
    gradient,
    sortedColorStops,
    updateName,
    updateConfig,
    updateGradientType,
    addColorStop,
    removeColorStop,
    updateColorStop,
    updateColorStopColor,
    updateColorStopPosition,
    updateColorStopAlpha,
    reorderColorStops,
    resetGradient,
    generatedCode,
    cssGradient,
    previewStyle,
    canRemoveStop,
    canAddStop
  } = useGradient(initialGradient);

  // Handle gradient name change
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    updateName(e.target.value);
  }, [updateName]);

  // Handle gradient type change
  const handleGradientTypeChange = useCallback((type: 'linear' | 'radial' | 'conic') => {
    updateGradientType(type);
  }, [updateGradientType]);

  // Handle color stops change
  const handleColorStopsChange = useCallback((colorStops: any[]) => {
    // This is handled by individual color stop updates
    // The ColorStopManager will call updateColorStop, addColorStop, removeColorStop directly
  }, []);

  // Create handlers for ColorStopManager
  const colorStopHandlers = {
    onAdd: addColorStop,
    onRemove: removeColorStop,
    onUpdate: updateColorStop,
    onUpdateColor: updateColorStopColor,
    onUpdatePosition: updateColorStopPosition,
    onUpdateAlpha: updateColorStopAlpha,
    onReorder: reorderColorStops
  };

  // Handle save functionality
  const handleSave = useCallback(async () => {
    if (!onSave) return;
    
    setIsSaving(true);
    try {
      await onSave(gradient);
      showToast('Gradient saved successfully!', 'success');
    } catch (error) {
      showToast('Failed to save gradient', 'error');
    } finally {
      setIsSaving(false);
    }
  }, [gradient, onSave, showToast]);

  // Handle export functionality
  const handleExport = useCallback((code: string, format: string) => {
    if (onExport) {
      onExport(gradient, format);
    }
  }, [gradient, onExport]);

  // Handle reset functionality
  const handleReset = useCallback(() => {
    resetGradient();
    showToast('Gradient reset to default', 'info');
  }, [resetGradient, showToast]);

  // Handle share functionality
  const handleShare = useCallback(async () => {
    try {
      const shareData = {
        title: `${gradient.name} - CSS Gradient`,
        text: `Check out this gradient: ${gradient.name}`,
        url: window.location.href
      };

      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy URL to clipboard
        await navigator.clipboard.writeText(window.location.href);
        showToast('Gradient URL copied to clipboard!', 'success');
      }
    } catch (error) {
      showToast('Failed to share gradient', 'error');
    }
  }, [gradient.name, showToast]);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">Gradient Builder</h1>
              <p className="text-muted-foreground">
                Create beautiful CSS gradients with an intuitive interface
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleReset}
                size="sm"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              
              <Button
                variant="outline"
                onClick={handleShare}
                size="sm"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              
              {onSave && (
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  size="sm"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save'}
                </Button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Controls */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-6"
          >
            {/* Gradient Name */}
            <div className="space-y-2">
              <Label htmlFor="gradient-name" className="text-lg font-semibold">
                Gradient Name
              </Label>
              <Input
                id="gradient-name"
                value={gradient.name}
                onChange={handleNameChange}
                placeholder="Enter gradient name..."
                className="text-lg"
              />
            </div>

            <Separator />

            {/* Gradient Type Selector */}
            <GradientTypeSelector
              gradientType={gradient.config.type}
              onGradientTypeChange={handleGradientTypeChange}
              showIcons={true}
            />

            <Separator />

            {/* Gradient Controls */}
            <GradientControls
              config={gradient.config}
              onConfigChange={updateConfig}
            />

            <Separator />

            {/* Color Stop Manager */}
            <ScrollArea className="h-96">
              <ColorStopManager
                colorStops={gradient.colorStops}
                onColorStopsChange={handleColorStopsChange}
                onAddColorStop={colorStopHandlers.onAdd}
                onRemoveColorStop={colorStopHandlers.onRemove}
                onUpdateColorStop={colorStopHandlers.onUpdate}
                maxStops={10}
                minStops={2}
              />
            </ScrollArea>
          </motion.div>

          {/* Right Panel - Preview and Code */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Gradient Preview */}
            <GradientPreview
              gradient={gradient}
              backgroundPattern="transparent"
            />

            <Separator />

            {/* Code Output */}
            <CodeOutput
              gradient={gradient}
              activeFormat={activeCodeFormat}
              onFormatChange={setActiveCodeFormat}
              onCopy={handleExport}
            />
          </motion.div>
        </div>

        {/* Mobile-friendly bottom actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 lg:hidden"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              variant="outline"
              onClick={handleReset}
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            
            <Button
              variant="outline"
              onClick={handleShare}
              size="sm"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            
            {onSave && (
              <Button
                onClick={handleSave}
                disabled={isSaving}
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
