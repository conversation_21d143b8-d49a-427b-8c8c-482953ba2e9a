import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Copy, Check, Download, Code } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { CodeOutputProps } from '@/types/types';
import { codeGeneration } from '@/utils/codeGeneration';
import { useCopyState } from '@/hooks/useCopyState';
import { useCustomToast } from '@/hooks/useCustomToast';
import { cn } from '@/lib/utils';

// Simple syntax highlighting for different languages
const highlightCode = (code: string, language: string): string => {
  switch (language) {
    case 'css':
      return code
        .replace(/(background|background-image|color|linear-gradient|radial-gradient|conic-gradient):/g, '<span class="text-blue-600 dark:text-blue-400">$1:</span>')
        .replace(/(#[a-fA-F0-9]{3,8})/g, '<span class="text-green-600 dark:text-green-400">$1</span>')
        .replace(/(\d+(?:\.\d+)?(?:deg|%|px))/g, '<span class="text-purple-600 dark:text-purple-400">$1</span>')
        .replace(/(\/\*.*?\*\/)/gs, '<span class="text-gray-500 dark:text-gray-400">$1</span>');
    
    case 'sass':
      return code
        .replace(/(\$[\w-]+)/g, '<span class="text-orange-600 dark:text-orange-400">$1</span>')
        .replace(/@(mixin|include)/g, '<span class="text-pink-600 dark:text-pink-400">@$1</span>')
        .replace(/(\/\/.*$)/gm, '<span class="text-gray-500 dark:text-gray-400">$1</span>')
        .replace(/(#[a-fA-F0-9]{3,8})/g, '<span class="text-green-600 dark:text-green-400">$1</span>')
        .replace(/(\d+(?:\.\d+)?(?:deg|%|px))/g, '<span class="text-purple-600 dark:text-purple-400">$1</span>');
    
    case 'tailwind':
      return code
        .replace(/(bg-gradient-\w+|from-|via-|to-|bg-)/g, '<span class="text-cyan-600 dark:text-cyan-400">$1</span>')
        .replace(/(\[#[a-fA-F0-9]{3,8}\])/g, '<span class="text-green-600 dark:text-green-400">$1</span>');
    
    default:
      return code;
  }
};

const formatOptions = [
  { value: 'css', label: 'CSS', icon: Code, language: 'css' },
  { value: 'tailwind', label: 'Tailwind', icon: Code, language: 'tailwind' },
  { value: 'sass', label: 'Sass', icon: Code, language: 'sass' },
  { value: 'bootstrap', label: 'Bootstrap', icon: Code, language: 'css' }
] as const;

export const CodeOutput: React.FC<CodeOutputProps> = ({
  gradient,
  activeFormat,
  onFormatChange,
  onCopy
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { copiedStates, copyToClipboard } = useCopyState();
  const { showToast } = useCustomToast();

  // Generate all code formats
  const generatedCode = useMemo(() => {
    return codeGeneration.generateAllFormats(gradient);
  }, [gradient]);

  // Get current code based on active format
  const currentCode = useMemo(() => {
    switch (activeFormat) {
      case 'css':
        return generatedCode.css;
      case 'tailwind':
        return generatedCode.tailwind;
      case 'sass':
        return generatedCode.sass;
      case 'bootstrap':
        return generatedCode.bootstrap;
      default:
        return generatedCode.css;
    }
  }, [generatedCode, activeFormat]);

  // Handle copy functionality
  const handleCopy = async (code: string, format: string) => {
    try {
      await copyToClipboard(code, format);
      showToast(`${format.toUpperCase()} code copied to clipboard!`, 'success');
      onCopy?.(code, format);
    } catch (error) {
      showToast('Failed to copy code', 'error');
    }
  };

  // Handle download functionality
  const handleDownload = (code: string, format: string) => {
    const fileExtensions = {
      css: 'css',
      tailwind: 'html',
      sass: 'scss',
      bootstrap: 'css'
    };

    const extension = fileExtensions[format as keyof typeof fileExtensions] || 'txt';
    const filename = `${gradient.name.toLowerCase().replace(/\s+/g, '-')}-gradient.${extension}`;
    
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showToast(`Code downloaded as ${filename}`, 'success');
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-lg font-semibold">Generated Code</Label>
          <p className="text-sm text-muted-foreground">
            Copy or download the gradient code in your preferred format
          </p>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'Collapse' : 'Expand'}
        </Button>
      </div>

      <Tabs value={activeFormat} onValueChange={onFormatChange}>
        <TabsList className="grid w-full grid-cols-4">
          {formatOptions.map((option) => (
            <TabsTrigger key={option.value} value={option.value}>
              <option.icon className="h-4 w-4 mr-1" />
              {option.label}
            </TabsTrigger>
          ))}
        </TabsList>

        <AnimatePresence mode="wait">
          {formatOptions.map((option) => (
            <TabsContent key={option.value} value={option.value} className="mt-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-3"
              >
                {/* Code Block */}
                <div className="relative">
                  <div
                    className={cn(
                      "relative rounded-lg border border-border bg-muted/50 p-4 font-mono text-sm overflow-auto",
                      isExpanded ? "max-h-96" : "max-h-32"
                    )}
                  >
                    <pre
                      className="whitespace-pre-wrap break-all"
                      dangerouslySetInnerHTML={{
                        __html: highlightCode(
                          option.value === 'css' ? generatedCode.css :
                          option.value === 'tailwind' ? generatedCode.tailwind :
                          option.value === 'sass' ? generatedCode.sass :
                          generatedCode.bootstrap,
                          option.language
                        )
                      }}
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="absolute top-2 right-2 flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(currentCode, option.value)}
                      className="h-8 w-8 p-0"
                    >
                      <AnimatePresence mode="wait">
                        {copiedStates[option.value] ? (
                          <motion.div
                            key="check"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                          >
                            <Check className="h-3 w-3 text-green-600" />
                          </motion.div>
                        ) : (
                          <motion.div
                            key="copy"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                          >
                            <Copy className="h-3 w-3" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownload(currentCode, option.value)}
                      className="h-8 w-8 p-0"
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Format-specific Information */}
                <div className="text-xs text-muted-foreground">
                  {option.value === 'css' && (
                    <p>Standard CSS gradient syntax. Use with the <code>background</code> or <code>background-image</code> property.</p>
                  )}
                  {option.value === 'tailwind' && (
                    <p>Tailwind CSS utility classes. Add to your HTML elements or use arbitrary values for complex gradients.</p>
                  )}
                  {option.value === 'sass' && (
                    <p>Sass/SCSS with variables and mixins. Include the mixin in your stylesheets for reusable gradients.</p>
                  )}
                  {option.value === 'bootstrap' && (
                    <p>Bootstrap-compatible CSS classes. Use the utility classes or apply the gradient class to elements.</p>
                  )}
                </div>

                {/* Usage Example */}
                <div className="p-3 rounded-lg bg-muted/30 border border-border">
                  <Label className="text-xs font-medium mb-1 block">Usage Example:</Label>
                  <code className="text-xs">
                    {option.value === 'css' && `.my-element { background: ${generatedCode.css}; }`}
                    {option.value === 'tailwind' && `<div class="${generatedCode.tailwind}">Content</div>`}
                    {option.value === 'sass' && `@include ${gradient.name.toLowerCase().replace(/\s+/g, '-')}-gradient;`}
                    {option.value === 'bootstrap' && `<div class="gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')}">Content</div>`}
                  </code>
                </div>
              </motion.div>
            </TabsContent>
          ))}
        </AnimatePresence>
      </Tabs>
    </div>
  );
};
