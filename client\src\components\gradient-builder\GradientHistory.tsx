import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  History, 
  Heart, 
  Trash2, 
  Download, 
  Upload, 
  Search,
  Clock,
  Star
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGradientHistory, GradientHistoryItem } from '@/hooks/useGradientHistory';
import { GradientState } from '@/types/types';
import { codeGeneration } from '@/utils/codeGeneration';
import { useCustomToast } from '@/hooks/useCustomToast';

interface GradientHistoryProps {
  onSelectGradient: (gradient: GradientState) => void;
  className?: string;
}

interface HistoryCardProps {
  item: GradientHistoryItem;
  onSelect: (gradient: GradientState) => void;
  onToggleFavorite: (id: string) => void;
  onRemove: (id: string) => void;
}

const HistoryCard: React.FC<HistoryCardProps> = ({
  item,
  onSelect,
  onToggleFavorite,
  onRemove
}) => {
  const gradientCSS = codeGeneration.generateCSSGradient(item.gradient);
  const timeAgo = getTimeAgo(item.timestamp);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      className="group cursor-pointer"
    >
      <div className="relative overflow-hidden rounded-lg border border-[var(--border)] bg-[var(--card-background)] hover:border-[var(--primary)] transition-all duration-200">
        {/* Gradient Preview */}
        <div
          className="h-20 w-full"
          style={{ background: gradientCSS }}
          onClick={() => onSelect(item.gradient)}
        />
        
        {/* Content */}
        <div className="p-3">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0" onClick={() => onSelect(item.gradient)}>
              <h3 className="font-medium text-sm text-[var(--card-headline)] truncate">
                {item.gradient.name}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <Clock className="h-3 w-3 text-[var(--card-paragraph)]" />
                <span className="text-xs text-[var(--card-paragraph)]">
                  {timeAgo}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleFavorite(item.id);
                }}
                className="h-6 w-6 p-0"
              >
                <Heart 
                  className={`h-3 w-3 ${
                    item.isFavorite 
                      ? 'text-red-500 fill-current' 
                      : 'text-[var(--card-paragraph)]'
                  }`} 
                />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove(item.id);
                }}
                className="h-6 w-6 p-0 text-[var(--card-paragraph)] hover:text-destructive"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          {/* Gradient Info */}
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto">
              {item.gradient.config.type}
            </Badge>
            <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto">
              {item.gradient.colorStops.length} stops
            </Badge>
            {item.isFavorite && (
              <Star className="h-3 w-3 text-yellow-500 fill-current" />
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const GradientHistory: React.FC<GradientHistoryProps> = ({
  onSelectGradient,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('recent');
  const { showToast } = useCustomToast();
  
  const {
    history,
    isLoading,
    removeFromHistory,
    toggleFavorite,
    clearHistory,
    clearNonFavorites,
    getFavorites,
    getRecent,
    searchHistory,
    exportHistory,
    importHistory,
    totalCount,
    favoriteCount
  } = useGradientHistory();

  // Get filtered items based on active tab and search
  const getFilteredItems = () => {
    let items: GradientHistoryItem[] = [];
    
    if (searchQuery.trim()) {
      items = searchHistory(searchQuery);
    } else {
      switch (activeTab) {
        case 'recent':
          items = getRecent(20);
          break;
        case 'favorites':
          items = getFavorites();
          break;
        case 'all':
          items = history;
          break;
        default:
          items = getRecent(20);
      }
    }
    
    return items;
  };

  const filteredItems = getFilteredItems();

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      await importHistory(file);
      showToast('History imported successfully!', 'success');
    } catch (error) {
      showToast('Failed to import history', 'error');
    }
    
    // Reset file input
    event.target.value = '';
  };

  const handleClearHistory = () => {
    if (activeTab === 'favorites') {
      clearNonFavorites();
      showToast('Non-favorite gradients cleared', 'success');
    } else {
      clearHistory();
      showToast('History cleared', 'success');
    }
  };

  if (isLoading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-8">
          <div className="text-[var(--paragraph)]">Loading history...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-lg font-semibold text-[var(--headline)]">
              Gradient History
            </Label>
            <p className="text-sm text-[var(--paragraph)]">
              {totalCount} gradients • {favoriteCount} favorites
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportHistory}
              disabled={totalCount === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            
            <label>
              <Button variant="outline" size="sm" asChild>
                <span>
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--paragraph)]" />
          <Input
            placeholder="Search gradients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-[var(--input-background)] border-[var(--input-border-color)] text-[var(--input-text)]"
          />
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="recent">
              <Clock className="h-4 w-4 mr-2" />
              Recent
            </TabsTrigger>
            <TabsTrigger value="favorites">
              <Heart className="h-4 w-4 mr-2" />
              Favorites
            </TabsTrigger>
            <TabsTrigger value="all">
              <History className="h-4 w-4 mr-2" />
              All
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-4">
            {/* Results Info */}
            <div className="flex items-center justify-between text-sm text-[var(--paragraph)] mb-4">
              <span>
                {filteredItems.length} gradient{filteredItems.length !== 1 ? 's' : ''} found
              </span>
              {filteredItems.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearHistory}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear {activeTab === 'favorites' ? 'non-favorites' : 'all'}
                </Button>
              )}
            </div>

            {/* History Grid */}
            <ScrollArea className="h-96">
              <AnimatePresence>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {filteredItems.map((item) => (
                    <HistoryCard
                      key={item.id}
                      item={item}
                      onSelect={onSelectGradient}
                      onToggleFavorite={toggleFavorite}
                      onRemove={removeFromHistory}
                    />
                  ))}
                </div>
              </AnimatePresence>
              
              {filteredItems.length === 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-8"
                >
                  <History className="h-12 w-12 text-[var(--paragraph)] mx-auto mb-4 opacity-50" />
                  <div className="text-[var(--paragraph)] mb-2">
                    {searchQuery ? 'No gradients found' : 'No gradients in history'}
                  </div>
                  <p className="text-sm text-[var(--paragraph)] opacity-75">
                    {searchQuery 
                      ? 'Try adjusting your search terms'
                      : 'Create some gradients to see them here'
                    }
                  </p>
                </motion.div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// Helper function to format time ago
const getTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return new Date(timestamp).toLocaleDateString();
};
