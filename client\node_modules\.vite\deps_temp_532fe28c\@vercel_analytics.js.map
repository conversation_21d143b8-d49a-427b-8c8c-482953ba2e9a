{"version": 3, "sources": ["../../@vercel/analytics/package.json", "../../@vercel/analytics/src/queue.ts", "../../@vercel/analytics/src/utils.ts", "../../@vercel/analytics/src/generic.ts"], "sourcesContent": ["{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.3.1\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.js\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup\",\n    \"dev\": \"tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"jest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"dependencies\": {\n    \"server-only\": \"^0.0.1\"\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.3.66\",\n    \"@swc/jest\": \"^0.2.26\",\n    \"@testing-library/jest-dom\": \"^5.16.5\",\n    \"@testing-library/react\": \"^14.0.0\",\n    \"@types/jest\": \"^29.5.2\",\n    \"@types/node\": \"^20.3.1\",\n    \"@types/react\": \"^18.2.14\",\n    \"@types/testing-library__jest-dom\": \"^5.14.6\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"jest\": \"^29.5.0\",\n    \"jest-environment-jsdom\": \"^29.5.0\",\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\",\n    \"tsup\": \"7.1.0\"\n  },\n  \"peerDependencies\": {\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19\"\n  },\n  \"peerDependenciesMeta\": {\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n} from './utils';\n\nexport const DEV_SCRIPT_URL =\n  'https://va.vercel-scripts.com/v1/script.debug.js';\nexport const PROD_SCRIPT_URL = '/_vercel/insights/script.js';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src =\n    props.scriptSrc || (isDevelopment() ? DEV_SCRIPT_URL : PROD_SCRIPT_URL);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({ route, path }: { route?: string; path?: string }): void {\n  window.va?.('pageview', {\n    route,\n    path,\n  });\n}\n\nexport { inject, track, pageview };\nexport type { AnalyticsProps };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n};\n"], "mappings": ";;;AACE,IAAA,OAAQ;AACR,IAAA,UAAW;ACFN,IAAM,YAAY,MAAY;AAEnC,MAAI,OAAO;AAAI;AAEf,SAAO,KAAK,SAAS,KAAK,QAAc;AACtC,KAAC,OAAO,MAAM,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;EAC7C;AACF;ACLO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;AACzD,MAAI;AACF,UAAM,MAAM;AACZ,QAAI,QAAQ,iBAAiB,QAAQ,QAAQ;AAC3C,aAAO;IACT;EACF,SAAS,GAAP;EAEF;AACA,SAAO;AACT;AAEO,SAAS,QAAQ,OAAa,QAAc;AACjD,MAAI,SAAS,QAAQ;AACnB,WAAO,MAAM,kBAAkB;AAC/B;EACF;AAEA,SAAO,MAAM;AACf;AAEO,SAAS,UAAgB;AAC9B,QAAM,OAAO,UAAU,IAAI,OAAO,MAAM,kBAAkB;AAC1D,SAAO,QAAQ;AACjB;AAEO,SAAS,eAAwB;AACtC,SAAO,QAAQ,MAAM;AACvB;AAEO,SAAS,gBAAyB;AACvC,SAAO,QAAQ,MAAM;AACvB;AAEA,SAAS,UACP,KACA,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,GACK;AACzB,SAAO;AACT;AAEO,SAAS,gBACd,YACA,SAG2D;AAC3D,MAAI,CAAC;AAAY,WAAO;AACxB,MAAI,QAAQ;AACZ,QAAM,kBAA4B,CAAC;AACnC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,UAAU,GAAG;AACrD,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,UAAI,QAAQ,OAAO;AACjB,gBAAQ,UAAU,KAAK,KAAK;MAC9B,OAAO;AACL,wBAAgB,KAAK,GAAG;MAC1B;IACF;EACF;AAEA,MAAI,gBAAgB,SAAS,KAAK,CAAC,QAAQ,OAAO;AAChD,UAAM;MACJ,2CAA2C,gBAAgB;QACzD;MACF,CAAC;IACH;EACF;AACA,SAAO;AACT;AC3DO,IAAM,iBACX;AACK,IAAM,kBAAkB;AAa/B,SAAS,OACP,QAEI;EACF,OAAO;AACT,GACM;AApCR,MAAA;AAqCE,MAAI,CAAC,UAAU;AAAG;AAElB,UAAQ,MAAM,IAAI;AAElB,YAAU;AAEV,MAAI,MAAM,YAAY;AACpB,KAAA,KAAA,OAAO,OAAP,OAAA,SAAA,GAAA,KAAA,QAAY,cAAc,MAAM,UAAA;EAClC;AAEA,QAAM,MACJ,MAAM,cAAc,cAAc,IAAI,iBAAiB;AAEzD,MAAI,SAAS,KAAK,cAAc,gBAAgB,GAAG,IAAI;AAAG;AAE1D,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,MAAM;AACb,SAAO,QAAQ;AACf,SAAO,QAAQ,OACb,QAAe,MAAM,YAAY,IAAI,MAAM,SAAS,KAAK;AAC3D,SAAO,QAAQ,OAAO;AAEtB,MAAI,MAAM,kBAAkB;AAC1B,WAAO,QAAQ,mBAAmB;EACpC;AACA,MAAI,MAAM,UAAU;AAClB,WAAO,QAAQ,WAAW,MAAM;EAClC;AACA,MAAI,MAAM,KAAK;AACb,WAAO,QAAQ,MAAM,MAAM;EAC7B;AAEA,SAAO,UAAU,MAAY;AAC3B,UAAM,eAAe,cAAc,IAC/B,+DACA;AAGJ,YAAQ;MACN,qDAAqD,GAAG,KAAK,YAAY;IAC3E;EACF;AAEA,MAAI,cAAc,KAAK,MAAM,UAAU,OAAO;AAC5C,WAAO,QAAQ,QAAQ;EACzB;AAEA,WAAS,KAAK,YAAY,MAAM;AAClC;AAQA,SAAS,MACPA,OACA,YACA,SAGM;AAnGR,MAAA,IAAA;AAoGE,MAAI,CAAC,UAAU,GAAG;AAChB,UAAM,MACJ;AAEF,QAAI,aAAa,GAAG;AAElB,cAAQ,KAAK,GAAG;IAClB,OAAO;AACL,YAAM,IAAI,MAAM,GAAG;IACrB;AAEA;EACF;AAEA,MAAI,CAAC,YAAY;AACf,KAAA,KAAA,OAAO,OAAP,OAAA,SAAA,GAAA,KAAA,QAAY,SAAS,EAAE,MAAAA,OAAM,QAAQ,CAAA;AACrC;EACF;AAEA,MAAI;AACF,UAAM,QAAQ,gBAAgB,YAAY;MACxC,OAAO,aAAa;IACtB,CAAC;AAED,KAAA,KAAA,OAAO,OAAP,OAAA,SAAA,GAAA,KAAA,QAAY,SAAS;MACnB,MAAAA;MACA,MAAM;MACN;IACF,CAAA;EACF,SAAS,KAAP;AACA,QAAI,eAAe,SAAS,cAAc,GAAG;AAE3C,cAAQ,MAAM,GAAG;IACnB;EACF;AACF;AAEA,SAAS,SAAS,EAAE,OAAO,KAAK,GAA4C;AAzI5E,MAAA;AA0IE,GAAA,KAAA,OAAO,OAAP,OAAA,SAAA,GAAA,KAAA,QAAY,YAAY;IACtB;IACA;EACF,CAAA;AACF;AAMA,IAAO,kBAAQ;EACb;EACA;AACF;", "names": ["name"]}