import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Star, Sparkles, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  gradientPresets, 
  gradientCategories, 
  getPresetsByCategory, 
  getPopularPresets, 
  searchPresets,
  GradientPreset 
} from '@/data/gradientPresets';
import { GradientState } from '@/types/types';
import { codeGeneration } from '@/utils/codeGeneration';

interface GradientPresetsProps {
  onSelectPreset: (preset: GradientPreset) => void;
  className?: string;
}

interface PresetCardProps {
  preset: GradientPreset;
  onSelect: (preset: GradientPreset) => void;
}

const PresetCard: React.FC<PresetCardProps> = ({ preset, onSelect }) => {
  const gradientCSS = useMemo(() => {
    const tempGradient: GradientState = {
      id: preset.id,
      name: preset.gradient.name,
      colorStops: preset.gradient.colorStops,
      config: preset.gradient.config,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return codeGeneration.generateCSSGradient(tempGradient);
  }, [preset]);

  const categoryInfo = gradientCategories.find(cat => cat.id === preset.category);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="group cursor-pointer"
      onClick={() => onSelect(preset)}
    >
      <div className="relative overflow-hidden rounded-lg border border-[var(--border)] bg-[var(--card-background)] hover:border-[var(--primary)] transition-all duration-200">
        {/* Gradient Preview */}
        <div
          className="h-24 w-full"
          style={{ background: gradientCSS }}
        />
        
        {/* Content */}
        <div className="p-3">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm text-[var(--card-headline)] truncate">
                {preset.name}
              </h3>
              <p className="text-xs text-[var(--card-paragraph)] line-clamp-2 mt-1">
                {preset.description}
              </p>
            </div>
            
            <div className="flex items-center gap-1 ml-2">
              {categoryInfo && (
                <span className="text-xs">{categoryInfo.icon}</span>
              )}
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span className="text-xs text-[var(--card-paragraph)]">
                  {preset.popularity}
                </span>
              </div>
            </div>
          </div>
          
          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {preset.tags.slice(0, 3).map((tag) => (
              <Badge 
                key={tag} 
                variant="secondary" 
                className="text-xs px-1.5 py-0.5 h-auto"
              >
                {tag}
              </Badge>
            ))}
            {preset.tags.length > 3 && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-auto">
                +{preset.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
        
        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileHover={{ opacity: 1, scale: 1 }}
            className="bg-white/90 dark:bg-black/90 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <Sparkles className="h-4 w-4 text-[var(--primary)]" />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export const GradientPresets: React.FC<GradientPresetsProps> = ({
  onSelectPreset,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showPopular, setShowPopular] = useState(false);

  // Filter presets based on search and category
  const filteredPresets = useMemo(() => {
    let presets = gradientPresets;

    // Apply search filter
    if (searchQuery.trim()) {
      presets = searchPresets(searchQuery);
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      presets = presets.filter(preset => preset.category === selectedCategory);
    }

    // Show popular presets
    if (showPopular) {
      presets = getPopularPresets(20);
    }

    return presets;
  }, [searchQuery, selectedCategory, showPopular]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setShowPopular(false);
  };

  const handleShowPopular = () => {
    setShowPopular(true);
    setSelectedCategory('all');
    setSearchQuery('');
  };

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-lg font-semibold text-[var(--headline)]">
              Gradient Presets
            </Label>
            <p className="text-sm text-[var(--paragraph)]">
              Choose from {gradientPresets.length} beautiful preset gradients
            </p>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShowPopular}
            className={showPopular ? 'bg-[var(--active)] text-[var(--active-text)]' : ''}
          >
            <Star className="h-4 w-4 mr-2" />
            Popular
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--paragraph)]" />
            <Input
              placeholder="Search presets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-[var(--input-background)] border-[var(--input-border-color)] text-[var(--input-text)]"
            />
          </div>
          
          {/* Category Filter */}
          <Select value={selectedCategory} onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-full sm:w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {gradientCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  <span className="flex items-center gap-2">
                    <span>{category.icon}</span>
                    {category.name}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Results Info */}
        <div className="flex items-center justify-between text-sm text-[var(--paragraph)]">
          <span>
            {filteredPresets.length} preset{filteredPresets.length !== 1 ? 's' : ''} found
          </span>
          {(searchQuery || selectedCategory !== 'all' || showPopular) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setShowPopular(false);
              }}
            >
              Clear filters
            </Button>
          )}
        </div>

        {/* Presets Grid */}
        <ScrollArea className="h-96">
          <AnimatePresence>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {filteredPresets.map((preset) => (
                <PresetCard
                  key={preset.id}
                  preset={preset}
                  onSelect={onSelectPreset}
                />
              ))}
            </div>
          </AnimatePresence>
          
          {filteredPresets.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-8"
            >
              <div className="text-[var(--paragraph)] mb-2">No presets found</div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                  setShowPopular(false);
                }}
              >
                Show all presets
              </Button>
            </motion.div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
};
