import { ColorConversion, ColorValidationResult, ColorFormat } from '@/types/types';

export const colorUtils = {
  // Enhanced HEX to RGB conversion with alpha support
  hexToRGB: (hex: string): { r: number; g: number; b: number; a?: number } => {
    // Remove # if present
    hex = hex.replace('#', '');

    // Handle 3-digit hex
    if (hex.length === 3) {
      hex = hex.split('').map(char => char + char).join('');
    }

    // Handle 8-digit hex with alpha
    if (hex.length === 8) {
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      const a = parseInt(hex.slice(6, 8), 16) / 255;
      return { r, g, b, a };
    }

    // Handle 6-digit hex
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return { r, g, b };
  },

  // RGB to HEX conversion with alpha support
  rgbToHex: (r: number, g: number, b: number, a?: number): string => {
    const toHex = (n: number) => {
      const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    const hex = `#${toHex(r)}${toHex(g)}${toHex(b)}`;

    if (a !== undefined && a < 1) {
      return hex + toHex(Math.round(a * 255));
    }

    return hex;
  },

  // Enhanced RGB to HSL conversion with alpha support
  rgbToHSL: (r: number, g: number, b: number, a?: number): { h: number; s: number; l: number; a?: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = ((g - b) / d + (g < b ? 6 : 0)) * 60;
          break;
        case g:
          h = ((b - r) / d + 2) * 60;
          break;
        case b:
          h = ((r - g) / d + 4) * 60;
          break;
      }
    }

    const result = {
      h: Math.round(h),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    };

    if (a !== undefined) {
      return { ...result, a };
    }

    return result;
  },

  // HSL to RGB conversion with alpha support
  hslToRGB: (h: number, s: number, l: number, a?: number): { r: number; g: number; b: number; a?: number } => {
    h = h % 360;
    s = Math.max(0, Math.min(100, s)) / 100;
    l = Math.max(0, Math.min(100, l)) / 100;

    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
    const m = l - c / 2;

    let r = 0, g = 0, b = 0;

    if (0 <= h && h < 60) {
      r = c; g = x; b = 0;
    } else if (60 <= h && h < 120) {
      r = x; g = c; b = 0;
    } else if (120 <= h && h < 180) {
      r = 0; g = c; b = x;
    } else if (180 <= h && h < 240) {
      r = 0; g = x; b = c;
    } else if (240 <= h && h < 300) {
      r = x; g = 0; b = c;
    } else if (300 <= h && h < 360) {
      r = c; g = 0; b = x;
    }

    const result = {
      r: Math.round((r + m) * 255),
      g: Math.round((g + m) * 255),
      b: Math.round((b + m) * 255),
    };

    if (a !== undefined) {
      return { ...result, a };
    }

    return result;
  },

  // HSL to HEX conversion
  hslToHex: (h: number, s: number, l: number, a?: number): string => {
    const rgb = colorUtils.hslToRGB(h, s, l, a);
    return colorUtils.rgbToHex(rgb.r, rgb.g, rgb.b, rgb.a);
  },

  // Comprehensive color format detection
  detectColorFormat: (color: string): ColorFormat | null => {
    color = color.trim();

    // HEX format
    if (/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/.test(color)) {
      return 'HEX';
    }

    // RGB/RGBA format
    if (/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/.test(color)) {
      return 'RGB';
    }

    // HSL/HSLA format
    if (/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+)?\s*\)$/.test(color)) {
      return 'HSL';
    }

    return null;
  },

  // Color validation with detailed error messages
  validateColor: (color: string): ColorValidationResult => {
    if (!color || typeof color !== 'string') {
      return { isValid: false, error: 'Color value is required' };
    }

    color = color.trim();
    const format = colorUtils.detectColorFormat(color);

    if (!format) {
      return { isValid: false, error: 'Invalid color format. Use HEX (#ff0000), RGB (rgb(255,0,0)), or HSL (hsl(0,100%,50%))' };
    }

    try {
      let normalizedColor: string;

      switch (format) {
        case 'HEX':
          const rgb = colorUtils.hexToRGB(color);
          normalizedColor = colorUtils.rgbToHex(rgb.r, rgb.g, rgb.b, rgb.a);
          break;
        case 'RGB':
          const rgbMatch = color.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*([\d.]+))?\s*\)/);
          if (!rgbMatch) {
            return { isValid: false, error: 'Invalid RGB format' };
          }
          const [, r, g, b, a] = rgbMatch;
          const rVal = parseInt(r), gVal = parseInt(g), bVal = parseInt(b);
          const aVal = a ? parseFloat(a) : undefined;

          if (rVal > 255 || gVal > 255 || bVal > 255) {
            return { isValid: false, error: 'RGB values must be between 0 and 255' };
          }
          if (aVal !== undefined && (aVal < 0 || aVal > 1)) {
            return { isValid: false, error: 'Alpha value must be between 0 and 1' };
          }

          normalizedColor = colorUtils.rgbToHex(rVal, gVal, bVal, aVal);
          break;
        case 'HSL':
          const hslMatch = color.match(/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(?:,\s*([\d.]+))?\s*\)/);
          if (!hslMatch) {
            return { isValid: false, error: 'Invalid HSL format' };
          }
          const [, h, s, l, hslA] = hslMatch;
          const hVal = parseInt(h), sVal = parseInt(s), lVal = parseInt(l);
          const hslAVal = hslA ? parseFloat(hslA) : undefined;

          if (hVal > 360) {
            return { isValid: false, error: 'Hue value must be between 0 and 360' };
          }
          if (sVal > 100 || lVal > 100) {
            return { isValid: false, error: 'Saturation and lightness must be between 0 and 100' };
          }
          if (hslAVal !== undefined && (hslAVal < 0 || hslAVal > 1)) {
            return { isValid: false, error: 'Alpha value must be between 0 and 1' };
          }

          normalizedColor = colorUtils.hslToHex(hVal, sVal, lVal, hslAVal);
          break;
        default:
          return { isValid: false, error: 'Unsupported color format' };
      }

      return { isValid: true, normalizedColor };
    } catch (error) {
      return { isValid: false, error: 'Failed to parse color value' };
    }
  },

  // Comprehensive color conversion function
  convertColor: (color: string, targetFormat: ColorFormat): string => {
    const validation = colorUtils.validateColor(color);
    if (!validation.isValid || !validation.normalizedColor) {
      throw new Error(validation.error || 'Invalid color');
    }

    const sourceFormat = colorUtils.detectColorFormat(color);
    if (!sourceFormat) {
      throw new Error('Unable to detect color format');
    }

    // If already in target format, return normalized version
    if (sourceFormat === targetFormat && targetFormat === 'HEX') {
      return validation.normalizedColor;
    }

    // Convert to RGB as intermediate format
    let rgb: { r: number; g: number; b: number; a?: number };

    switch (sourceFormat) {
      case 'HEX':
        rgb = colorUtils.hexToRGB(color);
        break;
      case 'RGB':
        const rgbMatch = color.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*([\d.]+))?\s*\)/);
        if (!rgbMatch) throw new Error('Invalid RGB format');
        const [, r, g, b, a] = rgbMatch;
        rgb = {
          r: parseInt(r),
          g: parseInt(g),
          b: parseInt(b),
          a: a ? parseFloat(a) : undefined
        };
        break;
      case 'HSL':
        const hslMatch = color.match(/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(?:,\s*([\d.]+))?\s*\)/);
        if (!hslMatch) throw new Error('Invalid HSL format');
        const [, h, s, l, hslA] = hslMatch;
        rgb = colorUtils.hslToRGB(parseInt(h), parseInt(s), parseInt(l), hslA ? parseFloat(hslA) : undefined);
        break;
      default:
        throw new Error('Unsupported source format');
    }

    // Convert to target format
    switch (targetFormat) {
      case 'HEX':
        return colorUtils.rgbToHex(rgb.r, rgb.g, rgb.b, rgb.a);
      case 'RGB':
        return rgb.a !== undefined
          ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${rgb.a})`
          : `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
      case 'HSL':
        const hsl = colorUtils.rgbToHSL(rgb.r, rgb.g, rgb.b, rgb.a);
        return hsl.a !== undefined
          ? `hsla(${hsl.h}, ${hsl.s}%, ${hsl.l}%, ${hsl.a})`
          : `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
      default:
        throw new Error('Unsupported target format');
    }
  },

  // Get full color conversion object
  getColorConversion: (color: string): ColorConversion => {
    const validation = colorUtils.validateColor(color);
    if (!validation.isValid || !validation.normalizedColor) {
      throw new Error(validation.error || 'Invalid color');
    }

    const rgb = colorUtils.hexToRGB(validation.normalizedColor);
    const hsl = colorUtils.rgbToHSL(rgb.r, rgb.g, rgb.b, rgb.a);

    return {
      hex: validation.normalizedColor,
      rgb,
      hsl
    };
  },

  // Parse color string to extract components
  parseColorString: (color: string): { format: ColorFormat; components: any } | null => {
    const format = colorUtils.detectColorFormat(color);
    if (!format) return null;

    switch (format) {
      case 'HEX':
        return { format, components: colorUtils.hexToRGB(color) };
      case 'RGB':
        const rgbMatch = color.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*([\d.]+))?\s*\)/);
        if (!rgbMatch) return null;
        const [, r, g, b, a] = rgbMatch;
        return {
          format,
          components: {
            r: parseInt(r),
            g: parseInt(g),
            b: parseInt(b),
            a: a ? parseFloat(a) : undefined
          }
        };
      case 'HSL':
        const hslMatch = color.match(/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(?:,\s*([\d.]+))?\s*\)/);
        if (!hslMatch) return null;
        const [, h, s, l, hslA] = hslMatch;
        return {
          format,
          components: {
            h: parseInt(h),
            s: parseInt(s),
            l: parseInt(l),
            a: hslA ? parseFloat(hslA) : undefined
          }
        };
      default:
        return null;
    }
  },

  // Extract unique color names from gradients data
  getUniqueColors: (gradients: any[]) => {
    const colorSet = new Set<string>();

    gradients.forEach((gradient) => {
      if (gradient.colorsname && Array.isArray(gradient.colorsname)) {
        gradient.colorsname.forEach((color: string) => {
          if (color && typeof color === "string") {
            colorSet.add(color.toLowerCase().trim());
          }
        });
      }
    });

    return Array.from(colorSet).sort();
  },

  // Get basic color categories for professional filtering
  getColorCategories: (colors: string[]) => {
    const categories: { [key: string]: string[] } = {
      "🔴 Red": [],
      "🩷 Pink": [],
      "🟠 Orange": [],
      "🟡 Yellow": [],
      "🟢 Green": [],
      "🔵 Blue": [],
      "🟣 Purple": [],
      "🟤 Brown": [],
      "⚫ Black": [],
      "⚪ White": [],
      "🔘 Gray": [],
      "🎨 Other": [],
    };

    colors.forEach((color) => {
      const lowerColor = color.toLowerCase();

      if (lowerColor.includes("red")) {
        categories["🔴 Red"].push(color);
      } else if (lowerColor.includes("pink")) {
        categories["🩷 Pink"].push(color);
      } else if (
        lowerColor.includes("orange") ||
        lowerColor.includes("peach")
      ) {
        categories["🟠 Orange"].push(color);
      } else if (lowerColor.includes("yellow")) {
        categories["🟡 Yellow"].push(color);
      } else if (
        lowerColor.includes("green") ||
        lowerColor.includes("olive") ||
        lowerColor.includes("teal") ||
        lowerColor.includes("cyan")
      ) {
        categories["🟢 Green"].push(color);
      } else if (lowerColor.includes("blue") || lowerColor.includes("indigo")) {
        categories["🔵 Blue"].push(color);
      } else if (
        lowerColor.includes("purple") ||
        lowerColor.includes("violet") ||
        lowerColor.includes("magenta")
      ) {
        categories["🟣 Purple"].push(color);
      } else if (lowerColor.includes("brown") || lowerColor.includes("beige")) {
        categories["🟤 Brown"].push(color);
      } else if (lowerColor.includes("black")) {
        categories["⚫ Black"].push(color);
      } else if (lowerColor.includes("white")) {
        categories["⚪ White"].push(color);
      } else if (lowerColor.includes("gray") || lowerColor.includes("grey")) {
        categories["🔘 Gray"].push(color);
      } else {
        categories["🎨 Other"].push(color);
      }
    });

    // Remove empty categories
    Object.keys(categories).forEach((key) => {
      if (categories[key].length === 0) {
        delete categories[key];
      }
    });

    return categories;
  },

  // Get basic colors only (most common ones for professional filtering)
  getBasicColors: (gradients: any[]) => {
    const allColors = colorUtils.getUniqueColors(gradients);
    const basicColorKeywords = [
      "red",
      "pink",
      "orange",
      "yellow",
      "green",
      "blue",
      "purple",
      "brown",
      "black",
      "white",
      "gray",
      "grey",
      "teal",
      "cyan",
    ];

    return allColors.filter((color) => {
      const lowerColor = color.toLowerCase();
      return basicColorKeywords.some((keyword) => lowerColor.includes(keyword));
    });
  },

  // Get simplified color categories with only basic colors
  getBasicColorCategories: (colors: string[]) => {
    const basicColors = colors.filter((color) => {
      const lowerColor = color.toLowerCase();
      const basicKeywords = [
        "red",
        "pink",
        "orange",
        "yellow",
        "green",
        "blue",
        "purple",
        "brown",
        "black",
        "white",
        "gray",
        "grey",
        "teal",
        "cyan",
      ];
      return basicKeywords.some((keyword) => lowerColor.includes(keyword));
    });

    return colorUtils.getColorCategories(basicColors);
  },
};
