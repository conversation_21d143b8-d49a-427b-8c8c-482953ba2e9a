import React, { useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RotateCcw, Move } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  GradientControlsProps, 
  LinearGradientConfig, 
  RadialGradientConfig, 
  ConicGradientConfig,
  RadialShape,
  RadialPosition 
} from '@/types/types';

const radialPositions: { value: RadialPosition; label: string }[] = [
  { value: 'center', label: 'Center' },
  { value: 'top', label: 'Top' },
  { value: 'bottom', label: 'Bottom' },
  { value: 'left', label: 'Left' },
  { value: 'right', label: 'Right' },
  { value: 'top-left', label: 'Top Left' },
  { value: 'top-right', label: 'Top Right' },
  { value: 'bottom-left', label: 'Bottom Left' },
  { value: 'bottom-right', label: 'Bottom Right' }
];

const LinearControls: React.FC<{
  config: LinearGradientConfig;
  onChange: (config: LinearGradientConfig) => void;
}> = ({ config, onChange }) => {
  const handleAngleChange = useCallback((angle: number[]) => {
    onChange({ ...config, angle: angle[0] });
  }, [config, onChange]);

  const resetAngle = useCallback(() => {
    onChange({ ...config, angle: 90 });
  }, [config, onChange]);

  // Convert angle to visual direction
  const getDirectionIndicator = (angle: number) => {
    const normalizedAngle = ((angle % 360) + 360) % 360;
    return {
      transform: `rotate(${normalizedAngle}deg)`,
    };
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-4"
    >
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Angle</Label>
        <Button
          variant="ghost"
          size="sm"
          onClick={resetAngle}
          className="h-6 px-2 text-xs"
        >
          <RotateCcw className="h-3 w-3 mr-1" />
          Reset
        </Button>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Slider
            value={[config.angle]}
            onValueChange={handleAngleChange}
            max={360}
            min={0}
            step={1}
            className="w-full"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <div
            className="w-6 h-6 border border-border rounded-full flex items-center justify-center"
            style={getDirectionIndicator(config.angle)}
          >
            <div className="w-1 h-3 bg-primary rounded-full" />
          </div>
          <span className="text-sm font-mono w-12 text-right">
            {config.angle}°
          </span>
        </div>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Adjust the angle to change the direction of the linear gradient.
      </p>
    </motion.div>
  );
};

const RadialControls: React.FC<{
  config: RadialGradientConfig;
  onChange: (config: RadialGradientConfig) => void;
}> = ({ config, onChange }) => {
  const handleShapeChange = useCallback((shape: string) => {
    onChange({ ...config, shape: shape as RadialShape });
  }, [config, onChange]);

  const handlePositionChange = useCallback((position: string) => {
    onChange({ ...config, position: position as RadialPosition });
  }, [config, onChange]);

  const handleCenterXChange = useCallback((centerX: number[]) => {
    onChange({ ...config, centerX: centerX[0] });
  }, [config, onChange]);

  const handleCenterYChange = useCallback((centerY: number[]) => {
    onChange({ ...config, centerY: centerY[0] });
  }, [config, onChange]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-4"
    >
      {/* Shape Selection */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Shape</Label>
        <Select value={config.shape} onValueChange={handleShapeChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="circle">Circle</SelectItem>
            <SelectItem value="ellipse">Ellipse</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Position Selection */}
      <div>
        <Label className="text-sm font-medium mb-2 block">Position</Label>
        <Select value={config.position} onValueChange={handlePositionChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {radialPositions.map((pos) => (
              <SelectItem key={pos.value} value={pos.value}>
                {pos.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Custom Center Position (if needed) */}
      {config.centerX !== undefined && config.centerY !== undefined && (
        <div className="space-y-3">
          <Label className="text-sm font-medium">Custom Center Position</Label>
          
          <div>
            <Label className="text-xs text-muted-foreground mb-1 block">
              Horizontal: {config.centerX}%
            </Label>
            <Slider
              value={[config.centerX]}
              onValueChange={handleCenterXChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
          
          <div>
            <Label className="text-xs text-muted-foreground mb-1 block">
              Vertical: {config.centerY}%
            </Label>
            <Slider
              value={[config.centerY]}
              onValueChange={handleCenterYChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      )}
      
      <p className="text-xs text-muted-foreground">
        Radial gradients emanate from a center point. Choose the shape and position.
      </p>
    </motion.div>
  );
};

const ConicControls: React.FC<{
  config: ConicGradientConfig;
  onChange: (config: ConicGradientConfig) => void;
}> = ({ config, onChange }) => {
  const handleStartAngleChange = useCallback((startAngle: number[]) => {
    onChange({ ...config, startAngle: startAngle[0] });
  }, [config, onChange]);

  const handleCenterXChange = useCallback((centerX: number[]) => {
    onChange({ ...config, centerX: centerX[0] });
  }, [config, onChange]);

  const handleCenterYChange = useCallback((centerY: number[]) => {
    onChange({ ...config, centerY: centerY[0] });
  }, [config, onChange]);

  const resetAngle = useCallback(() => {
    onChange({ ...config, startAngle: 0 });
  }, [config, onChange]);

  const centerPosition = useCallback(() => {
    onChange({ ...config, centerX: 50, centerY: 50 });
  }, [config, onChange]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-4"
    >
      {/* Start Angle */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium">Start Angle</Label>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetAngle}
            className="h-6 px-2 text-xs"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Slider
              value={[config.startAngle]}
              onValueChange={handleStartAngleChange}
              max={360}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
          <span className="text-sm font-mono w-12 text-right">
            {config.startAngle}°
          </span>
        </div>
      </div>

      {/* Center Position */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label className="text-sm font-medium">Center Position</Label>
          <Button
            variant="ghost"
            size="sm"
            onClick={centerPosition}
            className="h-6 px-2 text-xs"
          >
            <Move className="h-3 w-3 mr-1" />
            Center
          </Button>
        </div>
        
        <div className="space-y-3">
          <div>
            <Label className="text-xs text-muted-foreground mb-1 block">
              Horizontal: {config.centerX}%
            </Label>
            <Slider
              value={[config.centerX]}
              onValueChange={handleCenterXChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
          
          <div>
            <Label className="text-xs text-muted-foreground mb-1 block">
              Vertical: {config.centerY}%
            </Label>
            <Slider
              value={[config.centerY]}
              onValueChange={handleCenterYChange}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Conic gradients rotate colors around a center point. Adjust the start angle and center position.
      </p>
    </motion.div>
  );
};

export const GradientControls: React.FC<GradientControlsProps> = ({
  config,
  onConfigChange
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-author font-semibold tracking-tight">Gradient Controls</h3>
      
      <AnimatePresence mode="wait">
        {config.type === 'linear' && (
          <LinearControls
            key="linear"
            config={config}
            onChange={onConfigChange}
          />
        )}
        
        {config.type === 'radial' && (
          <RadialControls
            key="radial"
            config={config}
            onChange={onConfigChange}
          />
        )}
        
        {config.type === 'conic' && (
          <ConicControls
            key="conic"
            config={config}
            onChange={onConfigChange}
          />
        )}
      </AnimatePresence>
    </div>
  );
};
