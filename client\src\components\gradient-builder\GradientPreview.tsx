import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Grid, Square, Circle, Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { GradientPreviewProps } from '@/types/types';
import { codeGeneration } from '@/utils/codeGeneration';
import { cn } from '@/lib/utils';

type BackgroundPattern = 'transparent' | 'white' | 'black' | 'checkerboard';

const backgroundPatterns: { 
  value: BackgroundPattern; 
  label: string; 
  icon: React.ReactNode;
  style: React.CSSProperties;
}[] = [
  {
    value: 'transparent',
    label: 'Transparent',
    icon: <Grid className="h-4 w-4" />,
    style: {
      backgroundImage: `
        linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
      `,
      backgroundSize: '20px 20px',
      backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
    }
  },
  {
    value: 'white',
    label: 'White',
    icon: <Square className="h-4 w-4" />,
    style: { backgroundColor: '#ffffff' }
  },
  {
    value: 'black',
    label: 'Black',
    icon: <Square className="h-4 w-4 fill-current" />,
    style: { backgroundColor: '#000000' }
  },
  {
    value: 'checkerboard',
    label: 'Checkerboard',
    icon: <Grid className="h-4 w-4" />,
    style: {
      backgroundImage: `
        linear-gradient(45deg, #ccc 25%, transparent 25%),
        linear-gradient(-45deg, #ccc 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #ccc 75%),
        linear-gradient(-45deg, transparent 75%, #ccc 75%)
      `,
      backgroundSize: '30px 30px',
      backgroundPosition: '0 0, 0 15px, 15px -15px, -15px 0px'
    }
  }
];

export const GradientPreview: React.FC<GradientPreviewProps> = ({
  gradient,
  backgroundPattern = 'transparent',
  className
}) => {
  const [currentBackground, setCurrentBackground] = useState<BackgroundPattern>(backgroundPattern);
  const [showOverlay, setShowOverlay] = useState(false);
  const [previewMode, setPreviewMode] = useState<'fill' | 'text'>('fill');

  // Generate the gradient CSS
  const gradientCSS = useMemo(() => {
    return codeGeneration.generateCSSGradient(gradient);
  }, [gradient]);

  // Get background pattern style
  const backgroundStyle = useMemo(() => {
    const pattern = backgroundPatterns.find(p => p.value === currentBackground);
    return pattern?.style || {};
  }, [currentBackground]);

  // Preview style for fill mode
  const fillPreviewStyle = useMemo(() => ({
    background: gradientCSS,
    ...backgroundStyle
  }), [gradientCSS, backgroundStyle]);

  // Preview style for text mode
  const textPreviewStyle = useMemo(() => ({
    background: gradientCSS,
    WebkitBackgroundClip: 'text',
    backgroundClip: 'text',
    color: 'transparent',
    fontSize: '4rem',
    fontWeight: 'bold',
    lineHeight: 1,
    textAlign: 'center' as const
  }), [gradientCSS]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-lg font-author font-semibold tracking-tight">Preview</Label>
          <p className="text-sm text-muted-foreground font-author font-normal">
            {gradient.name}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Preview Mode Toggle */}
          <div className="flex rounded-md border border-border">
            <Button
              variant={previewMode === 'fill' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setPreviewMode('fill')}
              className="rounded-r-none"
            >
              <Square className="h-4 w-4 mr-1" />
              Fill
            </Button>
            <Button
              variant={previewMode === 'text' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setPreviewMode('text')}
              className="rounded-l-none"
            >
              Aa
            </Button>
          </div>

          {/* Background Pattern Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {backgroundPatterns.find(p => p.value === currentBackground)?.icon}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {backgroundPatterns.map((pattern) => (
                <DropdownMenuItem
                  key={pattern.value}
                  onClick={() => setCurrentBackground(pattern.value)}
                  className="flex items-center gap-2"
                >
                  {pattern.icon}
                  {pattern.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Overlay Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowOverlay(!showOverlay)}
          >
            {showOverlay ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Preview Area */}
      <motion.div
        className="relative rounded-lg border border-border overflow-hidden"
        style={{ minHeight: '200px' }}
        layout
      >
        {/* Background Pattern */}
        <div
          className="absolute inset-0"
          style={backgroundStyle}
        />

        {/* Gradient Preview */}
        <AnimatePresence mode="wait">
          {previewMode === 'fill' ? (
            <motion.div
              key="fill"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0"
              style={{ background: gradientCSS }}
            />
          ) : (
            <motion.div
              key="text"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0 flex items-center justify-center"
              style={backgroundStyle}
            >
              <div style={textPreviewStyle}>
                Gradient
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Info Overlay */}
        <AnimatePresence>
          {showOverlay && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 flex items-center justify-center"
            >
              <div className="bg-white/90 dark:bg-black/90 rounded-lg p-4 max-w-sm mx-4">
                <h4 className="font-semibold mb-2">Gradient Info</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>Type:</strong> {gradient.config.type}</p>
                  <p><strong>Stops:</strong> {gradient.colorStops.length}</p>
                  {gradient.config.type === 'linear' && (
                    <p><strong>Angle:</strong> {gradient.config.angle}°</p>
                  )}
                  {gradient.config.type === 'radial' && (
                    <>
                      <p><strong>Shape:</strong> {gradient.config.shape}</p>
                      <p><strong>Position:</strong> {gradient.config.position}</p>
                    </>
                  )}
                  {gradient.config.type === 'conic' && (
                    <>
                      <p><strong>Start Angle:</strong> {gradient.config.startAngle}°</p>
                      <p><strong>Center:</strong> {gradient.config.centerX}%, {gradient.config.centerY}%</p>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Responsive Size Indicators */}
        <div className="absolute bottom-2 left-2 flex gap-1">
          <div className="w-2 h-2 bg-white/50 rounded-full md:bg-green-500/50" />
          <div className="w-2 h-2 bg-white/50 rounded-full lg:bg-blue-500/50" />
          <div className="w-2 h-2 bg-white/50 rounded-full xl:bg-purple-500/50" />
        </div>
      </motion.div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="p-3 rounded-lg bg-muted">
          <div className="text-lg font-semibold">{gradient.colorStops.length}</div>
          <div className="text-xs text-muted-foreground">Color Stops</div>
        </div>
        <div className="p-3 rounded-lg bg-muted">
          <div className="text-lg font-semibold capitalize">{gradient.config.type}</div>
          <div className="text-xs text-muted-foreground">Gradient Type</div>
        </div>
        <div className="p-3 rounded-lg bg-muted">
          <div className="text-lg font-semibold">
            {gradient.config.type === 'linear' ? `${gradient.config.angle}°` :
             gradient.config.type === 'radial' ? gradient.config.shape :
             `${gradient.config.startAngle}°`}
          </div>
          <div className="text-xs text-muted-foreground">
            {gradient.config.type === 'linear' ? 'Angle' :
             gradient.config.type === 'radial' ? 'Shape' :
             'Start Angle'}
          </div>
        </div>
      </div>
    </div>
  );
};
