import { useState, useEffect, useCallback } from 'react';
import { GradientState } from '@/types/types';

const STORAGE_KEY = 'gradientHistory';
const MAX_HISTORY_SIZE = 50;

export interface GradientHistoryItem {
  id: string;
  gradient: GradientState;
  timestamp: number;
  isFavorite?: boolean;
}

export const useGradientHistory = () => {
  const [history, setHistory] = useState<GradientHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load history from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem(STORAGE_KEY);
      if (savedHistory) {
        const parsed = JSON.parse(savedHistory);
        // Validate and convert dates
        const validHistory = parsed.map((item: any) => ({
          ...item,
          gradient: {
            ...item.gradient,
            createdAt: new Date(item.gradient.createdAt),
            updatedAt: new Date(item.gradient.updatedAt)
          }
        }));
        setHistory(validHistory);
      }
    } catch (error) {
      console.warn('Failed to load gradient history:', error);
      setHistory([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save history to localStorage whenever it changes
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
      } catch (error) {
        console.warn('Failed to save gradient history:', error);
      }
    }
  }, [history, isLoading]);

  // Add gradient to history
  const addToHistory = useCallback((gradient: GradientState) => {
    const historyItem: GradientHistoryItem = {
      id: `history_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      gradient: {
        ...gradient,
        id: gradient.id || `gradient_${Date.now()}`,
        updatedAt: new Date()
      },
      timestamp: Date.now(),
      isFavorite: false
    };

    setHistory(prev => {
      // Remove duplicate if exists (same gradient configuration)
      const filtered = prev.filter(item => 
        !isGradientEqual(item.gradient, gradient)
      );
      
      // Add new item at the beginning
      const newHistory = [historyItem, ...filtered];
      
      // Limit history size
      return newHistory.slice(0, MAX_HISTORY_SIZE);
    });

    return historyItem.id;
  }, []);

  // Remove gradient from history
  const removeFromHistory = useCallback((historyId: string) => {
    setHistory(prev => prev.filter(item => item.id !== historyId));
  }, []);

  // Toggle favorite status
  const toggleFavorite = useCallback((historyId: string) => {
    setHistory(prev => prev.map(item => 
      item.id === historyId 
        ? { ...item, isFavorite: !item.isFavorite }
        : item
    ));
  }, []);

  // Clear all history
  const clearHistory = useCallback(() => {
    setHistory([]);
  }, []);

  // Clear non-favorite history
  const clearNonFavorites = useCallback(() => {
    setHistory(prev => prev.filter(item => item.isFavorite));
  }, []);

  // Get favorites only
  const getFavorites = useCallback(() => {
    return history.filter(item => item.isFavorite);
  }, [history]);

  // Get recent gradients (last 10)
  const getRecent = useCallback((limit: number = 10) => {
    return history.slice(0, limit);
  }, [history]);

  // Search history
  const searchHistory = useCallback((query: string) => {
    const lowercaseQuery = query.toLowerCase();
    return history.filter(item => 
      item.gradient.name.toLowerCase().includes(lowercaseQuery)
    );
  }, [history]);

  // Export history as JSON
  const exportHistory = useCallback(() => {
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      gradients: history.map(item => ({
        ...item,
        gradient: {
          ...item.gradient,
          createdAt: item.gradient.createdAt.toISOString(),
          updatedAt: item.gradient.updatedAt.toISOString()
        }
      }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gradient-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [history]);

  // Import history from JSON
  const importHistory = useCallback((file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const importData = JSON.parse(content);
          
          if (!importData.gradients || !Array.isArray(importData.gradients)) {
            throw new Error('Invalid file format');
          }
          
          const importedHistory = importData.gradients.map((item: any) => ({
            ...item,
            gradient: {
              ...item.gradient,
              createdAt: new Date(item.gradient.createdAt),
              updatedAt: new Date(item.gradient.updatedAt)
            }
          }));
          
          setHistory(prev => {
            // Merge with existing history, avoiding duplicates
            const merged = [...importedHistory, ...prev];
            const unique = merged.filter((item, index, arr) => 
              arr.findIndex(other => other.id === item.id) === index
            );
            
            return unique.slice(0, MAX_HISTORY_SIZE);
          });
          
          resolve();
        } catch (error) {
          reject(new Error('Failed to parse import file'));
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }, []);

  return {
    // State
    history,
    isLoading,
    
    // Actions
    addToHistory,
    removeFromHistory,
    toggleFavorite,
    clearHistory,
    clearNonFavorites,
    
    // Queries
    getFavorites,
    getRecent,
    searchHistory,
    
    // Import/Export
    exportHistory,
    importHistory,
    
    // Stats
    totalCount: history.length,
    favoriteCount: history.filter(item => item.isFavorite).length
  };
};

// Helper function to compare gradients for equality
const isGradientEqual = (gradient1: GradientState, gradient2: GradientState): boolean => {
  // Compare gradient type and configuration
  if (gradient1.config.type !== gradient2.config.type) return false;
  
  // Compare color stops
  if (gradient1.colorStops.length !== gradient2.colorStops.length) return false;
  
  const sortedStops1 = [...gradient1.colorStops].sort((a, b) => a.position - b.position);
  const sortedStops2 = [...gradient2.colorStops].sort((a, b) => a.position - b.position);
  
  for (let i = 0; i < sortedStops1.length; i++) {
    const stop1 = sortedStops1[i];
    const stop2 = sortedStops2[i];
    
    if (
      stop1.color !== stop2.color ||
      stop1.position !== stop2.position ||
      Math.abs(stop1.alpha - stop2.alpha) > 0.01
    ) {
      return false;
    }
  }
  
  // Compare configuration details
  switch (gradient1.config.type) {
    case 'linear':
      return gradient1.config.angle === gradient2.config.angle;
    case 'radial':
      return (
        gradient1.config.shape === gradient2.config.shape &&
        gradient1.config.position === gradient2.config.position
      );
    case 'conic':
      return (
        gradient1.config.startAngle === gradient2.config.startAngle &&
        gradient1.config.centerX === gradient2.config.centerX &&
        gradient1.config.centerY === gradient2.config.centerY
      );
    default:
      return true;
  }
};
