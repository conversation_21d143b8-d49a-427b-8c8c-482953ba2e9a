import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, GripVertical, Palette } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { ColorStopManagerProps, ColorStop, ColorFormat } from '@/types/types';
import { useColorConversion } from '@/hooks/useColorConversion';
import { colorUtils } from '@/utils/colorUtils';

interface ColorStopItemProps {
  colorStop: ColorStop;
  index: number;
  onUpdate: (id: string, updates: Partial<ColorStop>) => void;
  onRemove: (id: string) => void;
  canRemove: boolean;
  isDragging?: boolean;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

const ColorStopItem: React.FC<ColorStopItemProps> = ({
  colorStop,
  index,
  onUpdate,
  onRemove,
  canRemove,
  isDragging = false,
  onDragStart,
  onDragEnd
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { 
    currentColor, 
    currentFormat, 
    updateColor, 
    updateFormat, 
    allFormats,
    isValidColor,
    validationError 
  } = useColorConversion(colorStop.color, colorStop.format);

  const handleColorChange = useCallback((color: string, format: ColorFormat) => {
    updateColor(color, format);
    onUpdate(colorStop.id, { color, format });
  }, [colorStop.id, onUpdate, updateColor]);

  const handlePositionChange = useCallback((position: number[]) => {
    onUpdate(colorStop.id, { position: position[0] });
  }, [colorStop.id, onUpdate]);

  const handleAlphaChange = useCallback((alpha: number[]) => {
    onUpdate(colorStop.id, { alpha: alpha[0] / 100 });
  }, [colorStop.id, onUpdate]);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`relative border border-border rounded-lg p-4 bg-card ${
        isDragging ? 'shadow-lg scale-105' : ''
      }`}
      style={{ zIndex: isDragging ? 1000 : 1 }}
    >
      {/* Drag Handle */}
      <div
        className="absolute left-2 top-2 cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground"
        onMouseDown={onDragStart}
        onMouseUp={onDragEnd}
      >
        <GripVertical className="h-4 w-4" />
      </div>

      {/* Remove Button */}
      {canRemove && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-2 h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
          onClick={() => onRemove(colorStop.id)}
        >
          <X className="h-3 w-3" />
        </Button>
      )}

      <div className="ml-6 mr-6">
        {/* Color Preview and Basic Controls */}
        <div className="flex items-center gap-3 mb-3">
          <div
            className="w-8 h-8 rounded border border-border cursor-pointer focus:ring-2 focus:ring-ring focus:ring-offset-2"
            style={{ backgroundColor: currentColor }}
            onClick={() => setIsExpanded(!isExpanded)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setIsExpanded(!isExpanded);
              }
            }}
            tabIndex={0}
            role="button"
            aria-label={`Color preview: ${currentColor}. Click to ${isExpanded ? 'collapse' : 'expand'} color controls`}
            aria-expanded={isExpanded}
          />
          
          <div className="flex-1">
            <Label className="text-xs text-muted-foreground">
              Stop {index + 1} • {colorStop.position}%
            </Label>
            <div className="text-sm font-mono">{currentColor}</div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <Palette className="h-4 w-4" />
          </Button>
        </div>

        {/* Position Slider */}
        <div className="mb-3">
          <Label className="text-xs text-muted-foreground mb-1 block">
            Position: {colorStop.position}%
          </Label>
          <Slider
            value={[colorStop.position]}
            onValueChange={handlePositionChange}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        {/* Alpha Slider */}
        <div className="mb-3">
          <Label className="text-xs text-muted-foreground mb-1 block">
            Opacity: {Math.round(colorStop.alpha * 100)}%
          </Label>
          <Slider
            value={[colorStop.alpha * 100]}
            onValueChange={handleAlphaChange}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        {/* Expanded Color Controls */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-border pt-3 mt-3"
            >
              <Tabs value={currentFormat} onValueChange={(value) => updateFormat(value as ColorFormat)}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="HEX">HEX</TabsTrigger>
                  <TabsTrigger value="RGB">RGB</TabsTrigger>
                  <TabsTrigger value="HSL">HSL</TabsTrigger>
                </TabsList>

                <TabsContent value="HEX" className="mt-3">
                  <div className="space-y-2">
                    <Label className="text-xs">HEX Value</Label>
                    <Input
                      value={allFormats?.hex || currentColor}
                      onChange={(e) => handleColorChange(e.target.value, 'HEX')}
                      placeholder="#ff0000"
                      className={`font-mono ${!isValidColor ? 'border-destructive' : ''}`}
                    />
                    {!isValidColor && validationError && (
                      <p className="text-xs text-destructive">{validationError}</p>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="RGB" className="mt-3">
                  <div className="space-y-2">
                    <Label className="text-xs">RGB Value</Label>
                    <Input
                      value={allFormats?.rgb || currentColor}
                      onChange={(e) => handleColorChange(e.target.value, 'RGB')}
                      placeholder="rgb(255, 0, 0)"
                      className={`font-mono ${!isValidColor ? 'border-destructive' : ''}`}
                    />
                    {!isValidColor && validationError && (
                      <p className="text-xs text-destructive">{validationError}</p>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="HSL" className="mt-3">
                  <div className="space-y-2">
                    <Label className="text-xs">HSL Value</Label>
                    <Input
                      value={allFormats?.hsl || currentColor}
                      onChange={(e) => handleColorChange(e.target.value, 'HSL')}
                      placeholder="hsl(0, 100%, 50%)"
                      className={`font-mono ${!isValidColor ? 'border-destructive' : ''}`}
                    />
                    {!isValidColor && validationError && (
                      <p className="text-xs text-destructive">{validationError}</p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

interface ExtendedColorStopManagerProps extends ColorStopManagerProps {
  onAddColorStop?: () => void;
  onRemoveColorStop?: (stopId: string) => void;
  onUpdateColorStop?: (stopId: string, updates: Partial<ColorStop>) => void;
}

export const ColorStopManager: React.FC<ExtendedColorStopManagerProps> = ({
  colorStops,
  onColorStopsChange,
  onAddColorStop,
  onRemoveColorStop,
  onUpdateColorStop,
  maxStops = 10,
  minStops = 2
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const handleAddColorStop = useCallback(() => {
    if (onAddColorStop) {
      onAddColorStop();
    } else {
      // Fallback implementation
      if (colorStops.length >= maxStops) return;

      const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
      const newPosition = sortedStops.length > 0 ?
        (sortedStops[sortedStops.length - 1].position + sortedStops[0].position) / 2 : 50;

      const newStop: ColorStop = {
        id: Math.random().toString(36).substr(2, 9),
        color: '#ffffff',
        alpha: 1,
        position: newPosition,
        format: 'HEX'
      };

      onColorStopsChange([...colorStops, newStop]);
    }
  }, [onAddColorStop, colorStops, maxStops, onColorStopsChange]);

  const handleRemoveColorStop = useCallback((stopId: string) => {
    if (onRemoveColorStop) {
      onRemoveColorStop(stopId);
    } else {
      // Fallback implementation
      if (colorStops.length <= minStops) return;
      onColorStopsChange(colorStops.filter(stop => stop.id !== stopId));
    }
  }, [onRemoveColorStop, colorStops, minStops, onColorStopsChange]);

  const handleUpdateColorStop = useCallback((stopId: string, updates: Partial<ColorStop>) => {
    if (onUpdateColorStop) {
      onUpdateColorStop(stopId, updates);
    } else {
      // Fallback implementation
      onColorStopsChange(
        colorStops.map(stop =>
          stop.id === stopId ? { ...stop, ...updates } : stop
        )
      );
    }
  }, [onUpdateColorStop, colorStops, onColorStopsChange]);

  const sortedColorStops = [...colorStops].sort((a, b) => a.position - b.position);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Color Stops</h3>
          <p className="text-sm text-muted-foreground">
            {colorStops.length} of {maxStops} stops
          </p>
        </div>
        
        <Button
          onClick={handleAddColorStop}
          disabled={colorStops.length >= maxStops}
          size="sm"
          variant="outline"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Stop
        </Button>
      </div>

      <div className="space-y-3">
        <AnimatePresence>
          {sortedColorStops.map((colorStop, index) => (
            <ColorStopItem
              key={colorStop.id}
              colorStop={colorStop}
              index={index}
              onUpdate={handleUpdateColorStop}
              onRemove={handleRemoveColorStop}
              canRemove={colorStops.length > minStops}
              isDragging={draggedIndex === index}
              onDragStart={() => setDraggedIndex(index)}
              onDragEnd={() => setDraggedIndex(null)}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};
