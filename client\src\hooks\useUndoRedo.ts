import React, { useState, useCallback, useRef } from 'react';

export interface UndoRedoState<T> {
  past: T[];
  present: T;
  future: T[];
}

export interface UndoRedoActions {
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  reset: (newState: any) => void;
  clearHistory: () => void;
}

const MAX_HISTORY_SIZE = 50;

export function useUndoRedo<T>(
  initialState: T,
  isEqual?: (a: T, b: T) => boolean
): [T, (newState: T) => void, UndoRedoActions] {
  const [state, setState] = useState<UndoRedoState<T>>({
    past: [],
    present: initialState,
    future: []
  });

  const lastUpdateRef = useRef<number>(0);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  // Default equality function
  const defaultIsEqual = useCallback((a: T, b: T): boolean => {
    return JSON.stringify(a) === JSON.stringify(b);
  }, []);

  const equalityFn = isEqual || defaultIsEqual;

  // Set new state with history tracking
  const setStateWithHistory = useCallback((newState: T) => {
    // Clear any pending debounced update
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce rapid updates (within 500ms)
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateRef.current;
    
    if (timeSinceLastUpdate < 500) {
      // Debounce: replace the current state without adding to history
      debounceTimeoutRef.current = setTimeout(() => {
        setState(currentState => {
          if (equalityFn(currentState.present, newState)) {
            return currentState;
          }
          
          return {
            ...currentState,
            present: newState,
            future: [] // Clear future when new state is set
          };
        });
        lastUpdateRef.current = Date.now();
      }, 300);
    } else {
      // Add to history immediately
      setState(currentState => {
        if (equalityFn(currentState.present, newState)) {
          return currentState;
        }

        const newPast = [...currentState.past, currentState.present];
        
        // Limit history size
        if (newPast.length > MAX_HISTORY_SIZE) {
          newPast.shift();
        }

        return {
          past: newPast,
          present: newState,
          future: [] // Clear future when new state is set
        };
      });
      lastUpdateRef.current = now;
    }
  }, [equalityFn]);

  // Undo action
  const undo = useCallback(() => {
    setState(currentState => {
      if (currentState.past.length === 0) {
        return currentState;
      }

      const previous = currentState.past[currentState.past.length - 1];
      const newPast = currentState.past.slice(0, currentState.past.length - 1);

      return {
        past: newPast,
        present: previous,
        future: [currentState.present, ...currentState.future]
      };
    });
  }, []);

  // Redo action
  const redo = useCallback(() => {
    setState(currentState => {
      if (currentState.future.length === 0) {
        return currentState;
      }

      const next = currentState.future[0];
      const newFuture = currentState.future.slice(1);

      return {
        past: [...currentState.past, currentState.present],
        present: next,
        future: newFuture
      };
    });
  }, []);

  // Reset to a new state and clear history
  const reset = useCallback((newState: T) => {
    setState({
      past: [],
      present: newState,
      future: []
    });
    lastUpdateRef.current = Date.now();
  }, []);

  // Clear history but keep current state
  const clearHistory = useCallback(() => {
    setState(currentState => ({
      past: [],
      present: currentState.present,
      future: []
    }));
  }, []);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const actions: UndoRedoActions = {
    canUndo: state.past.length > 0,
    canRedo: state.future.length > 0,
    undo,
    redo,
    reset,
    clearHistory
  };

  return [state.present, setStateWithHistory, actions];
}

// Keyboard shortcut hook for undo/redo
export function useUndoRedoKeyboard(actions: UndoRedoActions) {
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && !event.shiftKey && event.key === 'z') {
        event.preventDefault();
        if (actions.canUndo) {
          actions.undo();
        }
      } else if (
        ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'Z') ||
        ((event.ctrlKey || event.metaKey) && event.key === 'y')
      ) {
        event.preventDefault();
        if (actions.canRedo) {
          actions.redo();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [actions]);
}
