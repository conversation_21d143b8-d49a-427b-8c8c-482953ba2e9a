import { GradientState, ColorStop } from '@/types/types';

export interface GradientPreset {
  id: string;
  name: string;
  category: string;
  description: string;
  gradient: Omit<GradientState, 'id' | 'createdAt' | 'updatedAt'>;
  tags: string[];
  popularity: number;
}

export const gradientCategories = [
  { id: 'sunset', name: 'Sunset & Dawn', icon: '🌅' },
  { id: 'ocean', name: 'Ocean & Water', icon: '🌊' },
  { id: 'neon', name: 'Neon & Electric', icon: '⚡' },
  { id: 'nature', name: 'Nature & Earth', icon: '🌿' },
  { id: 'cosmic', name: 'Cosmic & Space', icon: '🌌' },
  { id: 'warm', name: 'Warm Tones', icon: '🔥' },
  { id: 'cool', name: 'Cool Tones', icon: '❄️' },
  { id: 'monochrome', name: 'Monochrome', icon: '⚫' },
  { id: 'vibrant', name: 'Vibrant & Bold', icon: '🎨' },
  { id: 'pastel', name: 'Pastel & Soft', icon: '🌸' }
];

const createColorStop = (color: string, position: number, alpha: number = 1): ColorStop => ({
  id: Math.random().toString(36).substr(2, 9),
  color,
  alpha,
  position,
  format: 'HEX'
});

export const gradientPresets: GradientPreset[] = [
  // Sunset & Dawn
  {
    id: 'sunset-orange',
    name: 'Sunset Orange',
    category: 'sunset',
    description: 'Warm sunset colors transitioning from orange to pink',
    gradient: {
      name: 'Sunset Orange',
      colorStops: [
        createColorStop('#ff7e5f', 0),
        createColorStop('#feb47b', 100)
      ],
      config: { type: 'linear', angle: 45 }
    },
    tags: ['warm', 'orange', 'pink', 'sunset'],
    popularity: 95
  },
  {
    id: 'dawn-sky',
    name: 'Dawn Sky',
    category: 'sunset',
    description: 'Early morning sky with soft purple and orange hues',
    gradient: {
      name: 'Dawn Sky',
      colorStops: [
        createColorStop('#a8edea', 0),
        createColorStop('#fed6e3', 100)
      ],
      config: { type: 'linear', angle: 135 }
    },
    tags: ['soft', 'purple', 'orange', 'dawn'],
    popularity: 88
  },
  {
    id: 'golden-hour',
    name: 'Golden Hour',
    category: 'sunset',
    description: 'Rich golden sunset with deep orange tones',
    gradient: {
      name: 'Golden Hour',
      colorStops: [
        createColorStop('#f12711', 0),
        createColorStop('#f5af19', 100)
      ],
      config: { type: 'linear', angle: 90 }
    },
    tags: ['golden', 'orange', 'warm', 'sunset'],
    popularity: 92
  },

  // Ocean & Water
  {
    id: 'ocean-blue',
    name: 'Ocean Blue',
    category: 'ocean',
    description: 'Deep ocean blues from light to dark',
    gradient: {
      name: 'Ocean Blue',
      colorStops: [
        createColorStop('#2196F3', 0),
        createColorStop('#21CBF3', 100)
      ],
      config: { type: 'linear', angle: 180 }
    },
    tags: ['blue', 'ocean', 'water', 'cool'],
    popularity: 90
  },
  {
    id: 'tropical-water',
    name: 'Tropical Water',
    category: 'ocean',
    description: 'Crystal clear tropical water with turquoise tones',
    gradient: {
      name: 'Tropical Water',
      colorStops: [
        createColorStop('#00d2ff', 0),
        createColorStop('#3a7bd5', 100)
      ],
      config: { type: 'linear', angle: 120 }
    },
    tags: ['turquoise', 'tropical', 'blue', 'clear'],
    popularity: 87
  },
  {
    id: 'deep-sea',
    name: 'Deep Sea',
    category: 'ocean',
    description: 'Mysterious deep ocean with dark blue gradients',
    gradient: {
      name: 'Deep Sea',
      colorStops: [
        createColorStop('#141e30', 0),
        createColorStop('#243b55', 100)
      ],
      config: { type: 'linear', angle: 225 }
    },
    tags: ['dark', 'blue', 'deep', 'mysterious'],
    popularity: 82
  },

  // Neon & Electric
  {
    id: 'neon-pink',
    name: 'Neon Pink',
    category: 'neon',
    description: 'Electric pink and purple neon glow',
    gradient: {
      name: 'Neon Pink',
      colorStops: [
        createColorStop('#ff006e', 0),
        createColorStop('#8338ec', 100)
      ],
      config: { type: 'linear', angle: 45 }
    },
    tags: ['neon', 'pink', 'purple', 'electric'],
    popularity: 89
  },
  {
    id: 'cyber-blue',
    name: 'Cyber Blue',
    category: 'neon',
    description: 'Futuristic cyan and blue electric gradient',
    gradient: {
      name: 'Cyber Blue',
      colorStops: [
        createColorStop('#00f5ff', 0),
        createColorStop('#0066ff', 100)
      ],
      config: { type: 'linear', angle: 90 }
    },
    tags: ['cyber', 'blue', 'electric', 'futuristic'],
    popularity: 85
  },
  {
    id: 'electric-violet',
    name: 'Electric Violet',
    category: 'neon',
    description: 'Vibrant electric violet with pink accents',
    gradient: {
      name: 'Electric Violet',
      colorStops: [
        createColorStop('#4776e6', 0),
        createColorStop('#8e54e9', 100)
      ],
      config: { type: 'linear', angle: 135 }
    },
    tags: ['violet', 'electric', 'purple', 'vibrant'],
    popularity: 83
  },

  // Nature & Earth
  {
    id: 'forest-green',
    name: 'Forest Green',
    category: 'nature',
    description: 'Lush forest greens from light to dark',
    gradient: {
      name: 'Forest Green',
      colorStops: [
        createColorStop('#56ab2f', 0),
        createColorStop('#a8e6cf', 100)
      ],
      config: { type: 'linear', angle: 180 }
    },
    tags: ['green', 'forest', 'nature', 'earth'],
    popularity: 86
  },
  {
    id: 'autumn-leaves',
    name: 'Autumn Leaves',
    category: 'nature',
    description: 'Warm autumn colors with orange and red tones',
    gradient: {
      name: 'Autumn Leaves',
      colorStops: [
        createColorStop('#ff9a9e', 0),
        createColorStop('#fecfef', 50),
        createColorStop('#fecfef', 100)
      ],
      config: { type: 'linear', angle: 120 }
    },
    tags: ['autumn', 'orange', 'red', 'warm'],
    popularity: 84
  },

  // Cosmic & Space
  {
    id: 'galaxy',
    name: 'Galaxy',
    category: 'cosmic',
    description: 'Deep space colors with purple and blue tones',
    gradient: {
      name: 'Galaxy',
      colorStops: [
        createColorStop('#2c3e50', 0),
        createColorStop('#4a569d', 100)
      ],
      config: { type: 'radial', shape: 'circle', position: 'center' }
    },
    tags: ['galaxy', 'space', 'purple', 'cosmic'],
    popularity: 91
  },
  {
    id: 'nebula',
    name: 'Nebula',
    category: 'cosmic',
    description: 'Colorful nebula with pink and purple cosmic dust',
    gradient: {
      name: 'Nebula',
      colorStops: [
        createColorStop('#667eea', 0),
        createColorStop('#764ba2', 100)
      ],
      config: { type: 'conic', startAngle: 0, centerX: 50, centerY: 50 }
    },
    tags: ['nebula', 'cosmic', 'purple', 'space'],
    popularity: 88
  },

  // Warm Tones
  {
    id: 'warm-flame',
    name: 'Warm Flame',
    category: 'warm',
    description: 'Fiery warm colors from yellow to red',
    gradient: {
      name: 'Warm Flame',
      colorStops: [
        createColorStop('#ff9a56', 0),
        createColorStop('#ff6b6b', 100)
      ],
      config: { type: 'linear', angle: 45 }
    },
    tags: ['warm', 'fire', 'orange', 'red'],
    popularity: 87
  },

  // Cool Tones
  {
    id: 'ice-cold',
    name: 'Ice Cold',
    category: 'cool',
    description: 'Cool icy blues and whites',
    gradient: {
      name: 'Ice Cold',
      colorStops: [
        createColorStop('#74b9ff', 0),
        createColorStop('#0984e3', 100)
      ],
      config: { type: 'linear', angle: 180 }
    },
    tags: ['cool', 'ice', 'blue', 'cold'],
    popularity: 85
  },

  // Monochrome
  {
    id: 'silver-chrome',
    name: 'Silver Chrome',
    category: 'monochrome',
    description: 'Metallic silver gradient with chrome finish',
    gradient: {
      name: 'Silver Chrome',
      colorStops: [
        createColorStop('#bdc3c7', 0),
        createColorStop('#2c3e50', 100)
      ],
      config: { type: 'linear', angle: 90 }
    },
    tags: ['silver', 'chrome', 'metallic', 'monochrome'],
    popularity: 80
  },

  // Vibrant & Bold
  {
    id: 'rainbow',
    name: 'Rainbow',
    category: 'vibrant',
    description: 'Full spectrum rainbow gradient',
    gradient: {
      name: 'Rainbow',
      colorStops: [
        createColorStop('#ff0000', 0),
        createColorStop('#ff8000', 16.66),
        createColorStop('#ffff00', 33.33),
        createColorStop('#80ff00', 50),
        createColorStop('#00ff80', 66.66),
        createColorStop('#0080ff', 83.33),
        createColorStop('#8000ff', 100)
      ],
      config: { type: 'linear', angle: 90 }
    },
    tags: ['rainbow', 'colorful', 'vibrant', 'spectrum'],
    popularity: 93
  },

  // Pastel & Soft
  {
    id: 'cotton-candy',
    name: 'Cotton Candy',
    category: 'pastel',
    description: 'Soft pastel pink and blue like cotton candy',
    gradient: {
      name: 'Cotton Candy',
      colorStops: [
        createColorStop('#ffecd2', 0),
        createColorStop('#fcb69f', 100)
      ],
      config: { type: 'linear', angle: 135 }
    },
    tags: ['pastel', 'soft', 'pink', 'cotton'],
    popularity: 89
  }
];

// Utility functions
export const getPresetsByCategory = (category: string): GradientPreset[] => {
  return gradientPresets.filter(preset => preset.category === category);
};

export const getPopularPresets = (limit: number = 10): GradientPreset[] => {
  return gradientPresets
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, limit);
};

export const searchPresets = (query: string): GradientPreset[] => {
  const lowercaseQuery = query.toLowerCase();
  return gradientPresets.filter(preset => 
    preset.name.toLowerCase().includes(lowercaseQuery) ||
    preset.description.toLowerCase().includes(lowercaseQuery) ||
    preset.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};
