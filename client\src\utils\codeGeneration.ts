import { GradientState, GradientCodeOutput, ColorStop } from '@/types/types';
import { colorUtils } from './colorUtils';

export const codeGeneration = {
  // Generate CSS gradient string
  generateCSSGradient: (gradient: GradientState): string => {
    const { colorStops, config } = gradient;
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    
    const colorString = sortedStops.map(stop => {
      const color = stop.alpha < 1 
        ? colorUtils.convertColor(stop.color, 'RGB').replace('rgb(', 'rgba(').replace(')', `, ${stop.alpha})`)
        : stop.color;
      return `${color} ${stop.position}%`;
    }).join(', ');

    switch (config.type) {
      case 'linear':
        return `linear-gradient(${config.angle}deg, ${colorString})`;
      case 'radial':
        const position = config.position === 'center' ? 'center' : config.position;
        const shape = config.shape === 'circle' ? 'circle' : 'ellipse';
        return `radial-gradient(${shape} at ${position}, ${colorString})`;
      case 'conic':
        return `conic-gradient(from ${config.startAngle}deg at ${config.centerX}% ${config.centerY}%, ${colorString})`;
      default:
        return `linear-gradient(90deg, ${colorString})`;
    }
  },

  // Generate Tailwind CSS classes
  generateTailwindCSS: (gradient: GradientState): string => {
    const { colorStops, config } = gradient;
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    
    if (config.type === 'linear') {
      // Map angle to Tailwind direction
      const getDirection = (angle: number): string => {
        const normalizedAngle = ((angle % 360) + 360) % 360;
        if (normalizedAngle >= 337.5 || normalizedAngle < 22.5) return 'to-r';
        if (normalizedAngle >= 22.5 && normalizedAngle < 67.5) return 'to-br';
        if (normalizedAngle >= 67.5 && normalizedAngle < 112.5) return 'to-b';
        if (normalizedAngle >= 112.5 && normalizedAngle < 157.5) return 'to-bl';
        if (normalizedAngle >= 157.5 && normalizedAngle < 202.5) return 'to-l';
        if (normalizedAngle >= 202.5 && normalizedAngle < 247.5) return 'to-tl';
        if (normalizedAngle >= 247.5 && normalizedAngle < 292.5) return 'to-t';
        return 'to-tr';
      };

      const direction = getDirection(config.angle);
      const fromColor = sortedStops[0];
      const toColor = sortedStops[sortedStops.length - 1];
      
      let classes = `bg-gradient-${direction} from-[${fromColor.color}]`;
      
      // Add via colors for middle stops
      if (sortedStops.length > 2) {
        const viaStops = sortedStops.slice(1, -1);
        viaStops.forEach(stop => {
          classes += ` via-[${stop.color}]`;
        });
      }
      
      classes += ` to-[${toColor.color}]`;
      return classes;
    }
    
    // For radial and conic gradients, use arbitrary values
    const gradientCSS = codeGeneration.generateCSSGradient(gradient);
    return `bg-[${gradientCSS}]`;
  },

  // Generate Sass/SCSS code
  generateSassCode: (gradient: GradientState): string => {
    const { colorStops, config } = gradient;
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    
    let sassCode = `// ${gradient.name} Gradient\n`;
    
    // Define color variables
    sortedStops.forEach((stop, index) => {
      sassCode += `$color-${index + 1}: ${stop.color};\n`;
    });
    
    // Define gradient configuration
    switch (config.type) {
      case 'linear':
        sassCode += `$gradient-angle: ${config.angle}deg;\n`;
        break;
      case 'radial':
        sassCode += `$gradient-shape: ${config.shape};\n`;
        sassCode += `$gradient-position: ${config.position};\n`;
        break;
      case 'conic':
        sassCode += `$gradient-start-angle: ${config.startAngle}deg;\n`;
        sassCode += `$gradient-center: ${config.centerX}% ${config.centerY}%;\n`;
        break;
    }
    
    // Generate mixin
    sassCode += `\n@mixin ${gradient.name.toLowerCase().replace(/\s+/g, '-')}-gradient {\n`;
    sassCode += `  background: ${codeGeneration.generateCSSGradient(gradient)};\n`;
    sassCode += `}\n\n`;
    
    // Usage example
    sassCode += `// Usage:\n`;
    sassCode += `.gradient-element {\n`;
    sassCode += `  @include ${gradient.name.toLowerCase().replace(/\s+/g, '-')}-gradient;\n`;
    sassCode += `}`;
    
    return sassCode;
  },

  // Generate Bootstrap-compatible code
  generateBootstrapCode: (gradient: GradientState): string => {
    const gradientCSS = codeGeneration.generateCSSGradient(gradient);
    
    let bootstrapCode = `/* ${gradient.name} Gradient */\n`;
    bootstrapCode += `.gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')} {\n`;
    bootstrapCode += `  background: ${gradientCSS};\n`;
    bootstrapCode += `}\n\n`;
    
    // Add utility classes
    bootstrapCode += `/* Utility classes */\n`;
    bootstrapCode += `.bg-gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')} {\n`;
    bootstrapCode += `  background-image: ${gradientCSS} !important;\n`;
    bootstrapCode += `}\n\n`;
    
    bootstrapCode += `.text-gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')} {\n`;
    bootstrapCode += `  background: ${gradientCSS};\n`;
    bootstrapCode += `  -webkit-background-clip: text;\n`;
    bootstrapCode += `  background-clip: text;\n`;
    bootstrapCode += `  color: transparent;\n`;
    bootstrapCode += `}`;
    
    return bootstrapCode;
  },

  // Generate all code formats
  generateAllFormats: (gradient: GradientState): GradientCodeOutput => {
    return {
      css: codeGeneration.generateCSSGradient(gradient),
      tailwind: codeGeneration.generateTailwindCSS(gradient),
      sass: codeGeneration.generateSassCode(gradient),
      bootstrap: codeGeneration.generateBootstrapCode(gradient)
    };
  },

  // Generate inline style object for React
  generateInlineStyle: (gradient: GradientState): React.CSSProperties => {
    return {
      background: codeGeneration.generateCSSGradient(gradient)
    };
  },

  // Generate CSS custom properties
  generateCSSCustomProperties: (gradient: GradientState): string => {
    const { colorStops } = gradient;
    const sortedStops = [...colorStops].sort((a, b) => a.position - b.position);
    
    let cssVars = `:root {\n`;
    cssVars += `  --gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')}: ${codeGeneration.generateCSSGradient(gradient)};\n`;
    
    // Individual color variables
    sortedStops.forEach((stop, index) => {
      cssVars += `  --gradient-color-${index + 1}: ${stop.color};\n`;
    });
    
    cssVars += `}\n\n`;
    cssVars += `/* Usage */\n`;
    cssVars += `.gradient-element {\n`;
    cssVars += `  background: var(--gradient-${gradient.name.toLowerCase().replace(/\s+/g, '-')});\n`;
    cssVars += `}`;
    
    return cssVars;
  }
};
