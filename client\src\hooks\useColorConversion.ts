import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { ColorFormat, ColorConversion } from '@/types/types';
import { colorUtils } from '@/utils/colorUtils';

// Debounce utility
const useDebounce = <T extends any[]>(
  callback: (...args: T) => void,
  delay: number
) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  return useCallback((...args: T) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

export const useColorConversion = (initialColor: string = '#ff0000', initialFormat: ColorFormat = 'HEX') => {
  const [currentColor, setCurrentColor] = useState(initialColor);
  const [currentFormat, setCurrentFormat] = useState<ColorFormat>(initialFormat);
  const [isValidColor, setIsValidColor] = useState(true);
  const [validationError, setValidationError] = useState<string>('');
  const [isConverting, setIsConverting] = useState(false);

  // Validate and normalize color
  const validateAndNormalizeColor = useCallback((color: string) => {
    const validation = colorUtils.validateColor(color);
    setIsValidColor(validation.isValid);
    setValidationError(validation.error || '');
    return validation;
  }, []);

  // Debounced color validation
  const debouncedValidation = useDebounce((color: string) => {
    setIsConverting(false);
    validateAndNormalizeColor(color);
  }, 300);

  // Update color with validation
  const updateColor = useCallback((color: string, format?: ColorFormat) => {
    setIsConverting(true);
    setCurrentColor(color);
    
    if (format) {
      setCurrentFormat(format);
    }
    
    debouncedValidation(color);
  }, [debouncedValidation]);

  // Update format and convert color
  const updateFormat = useCallback((newFormat: ColorFormat) => {
    if (newFormat === currentFormat || !isValidColor) return;
    
    try {
      const convertedColor = colorUtils.convertColor(currentColor, newFormat);
      setCurrentColor(convertedColor);
      setCurrentFormat(newFormat);
    } catch (error) {
      console.warn('Failed to convert color format:', error);
    }
  }, [currentColor, currentFormat, isValidColor]);

  // Get color in specific format
  const getColorInFormat = useCallback((format: ColorFormat, color?: string): string => {
    const targetColor = color || currentColor;
    
    try {
      return colorUtils.convertColor(targetColor, format);
    } catch (error) {
      console.warn('Failed to get color in format:', error);
      return targetColor;
    }
  }, [currentColor]);

  // Get full color conversion object
  const colorConversion = useMemo((): ColorConversion | null => {
    if (!isValidColor) return null;
    
    try {
      return colorUtils.getColorConversion(currentColor);
    } catch (error) {
      return null;
    }
  }, [currentColor, isValidColor]);

  // Get color components for current format
  const colorComponents = useMemo(() => {
    if (!isValidColor || !colorConversion) return null;
    
    switch (currentFormat) {
      case 'HEX':
        return {
          format: 'HEX' as const,
          value: colorConversion.hex,
          components: colorConversion.rgb
        };
      case 'RGB':
        return {
          format: 'RGB' as const,
          value: colorConversion.rgb.a !== undefined 
            ? `rgba(${colorConversion.rgb.r}, ${colorConversion.rgb.g}, ${colorConversion.rgb.b}, ${colorConversion.rgb.a})`
            : `rgb(${colorConversion.rgb.r}, ${colorConversion.rgb.g}, ${colorConversion.rgb.b})`,
          components: colorConversion.rgb
        };
      case 'HSL':
        return {
          format: 'HSL' as const,
          value: colorConversion.hsl.a !== undefined
            ? `hsla(${colorConversion.hsl.h}, ${colorConversion.hsl.s}%, ${colorConversion.hsl.l}%, ${colorConversion.hsl.a})`
            : `hsl(${colorConversion.hsl.h}, ${colorConversion.hsl.s}%, ${colorConversion.hsl.l}%)`,
          components: colorConversion.hsl
        };
      default:
        return null;
    }
  }, [currentFormat, colorConversion, isValidColor]);

  // Get all format representations
  const allFormats = useMemo(() => {
    if (!colorConversion) return null;
    
    return {
      hex: colorConversion.hex,
      rgb: colorConversion.rgb.a !== undefined 
        ? `rgba(${colorConversion.rgb.r}, ${colorConversion.rgb.g}, ${colorConversion.rgb.b}, ${colorConversion.rgb.a})`
        : `rgb(${colorConversion.rgb.r}, ${colorConversion.rgb.g}, ${colorConversion.rgb.b})`,
      hsl: colorConversion.hsl.a !== undefined
        ? `hsla(${colorConversion.hsl.h}, ${colorConversion.hsl.s}%, ${colorConversion.hsl.l}%, ${colorConversion.hsl.a})`
        : `hsl(${colorConversion.hsl.h}, ${colorConversion.hsl.s}%, ${colorConversion.hsl.l}%)`
    };
  }, [colorConversion]);

  // Parse color input and detect format
  const parseColorInput = useCallback((input: string) => {
    const detectedFormat = colorUtils.detectColorFormat(input);
    const parsed = colorUtils.parseColorString(input);
    
    return {
      detectedFormat,
      parsed,
      isValid: detectedFormat !== null && parsed !== null
    };
  }, []);

  // Update individual color components
  const updateColorComponent = useCallback((component: string, value: number) => {
    if (!colorConversion) return;
    
    try {
      let newColor: string;
      
      switch (currentFormat) {
        case 'RGB':
          const rgb = { ...colorConversion.rgb };
          (rgb as any)[component] = value;
          newColor = colorUtils.rgbToHex(rgb.r, rgb.g, rgb.b, rgb.a);
          break;
        case 'HSL':
          const hsl = { ...colorConversion.hsl };
          (hsl as any)[component] = value;
          newColor = colorUtils.hslToHex(hsl.h, hsl.s, hsl.l, hsl.a);
          break;
        default:
          return;
      }
      
      const convertedColor = colorUtils.convertColor(newColor, currentFormat);
      updateColor(convertedColor, currentFormat);
    } catch (error) {
      console.warn('Failed to update color component:', error);
    }
  }, [colorConversion, currentFormat, updateColor]);

  // Reset to initial values
  const reset = useCallback(() => {
    setCurrentColor(initialColor);
    setCurrentFormat(initialFormat);
    setIsValidColor(true);
    setValidationError('');
    setIsConverting(false);
  }, [initialColor, initialFormat]);

  // Effect to validate initial color
  useEffect(() => {
    validateAndNormalizeColor(initialColor);
  }, [initialColor, validateAndNormalizeColor]);

  return {
    // Current state
    currentColor,
    currentFormat,
    isValidColor,
    validationError,
    isConverting,
    
    // Color data
    colorConversion,
    colorComponents,
    allFormats,
    
    // Actions
    updateColor,
    updateFormat,
    updateColorComponent,
    reset,
    
    // Utilities
    getColorInFormat,
    parseColorInput,
    
    // Available formats
    availableFormats: ['HEX', 'RGB', 'HSL'] as ColorFormat[]
  };
};
