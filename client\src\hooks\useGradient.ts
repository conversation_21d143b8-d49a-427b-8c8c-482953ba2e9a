import { useState, useCallback, useMemo } from 'react';
import { GradientState, ColorStop, GradientConfig, GradientType, ColorFormat } from '@/types/types';
import { colorUtils } from '@/utils/colorUtils';
import { codeGeneration } from '@/utils/codeGeneration';
import { useUndoRedo, useUndoRedoKeyboard } from './useUndoRedo';

// Generate unique ID for color stops and gradients
const generateId = () => Math.random().toString(36).substr(2, 9);

// Default gradient configuration
const createDefaultGradient = (): GradientState => ({
  id: generateId(),
  name: 'Custom Gradient',
  colorStops: [
    {
      id: generateId(),
      color: '#ff6b6b',
      alpha: 1,
      position: 0,
      format: 'HEX' as ColorFormat
    },
    {
      id: generateId(),
      color: '#4ecdc4',
      alpha: 1,
      position: 100,
      format: 'HEX' as ColorFormat
    }
  ],
  config: {
    type: 'linear',
    angle: 90
  },
  createdAt: new Date(),
  updatedAt: new Date()
});

export const useGradient = (initialGradient?: Partial<GradientState>) => {
  const defaultGradient = useMemo(() => {
    if (initialGradient) {
      return {
        ...createDefaultGradient(),
        ...initialGradient,
        updatedAt: new Date()
      };
    }
    return createDefaultGradient();
  }, [initialGradient]);

  // Use undo/redo functionality
  const [gradient, setGradient, undoRedoActions] = useUndoRedo(
    defaultGradient,
    (a, b) => {
      // Custom equality function for gradients
      return (
        a.name === b.name &&
        a.config.type === b.config.type &&
        JSON.stringify(a.config) === JSON.stringify(b.config) &&
        a.colorStops.length === b.colorStops.length &&
        a.colorStops.every((stop, index) => {
          const otherStop = b.colorStops[index];
          return (
            stop.color === otherStop.color &&
            stop.position === otherStop.position &&
            Math.abs(stop.alpha - otherStop.alpha) < 0.01
          );
        })
      );
    }
  );

  // Enable keyboard shortcuts for undo/redo
  useUndoRedoKeyboard(undoRedoActions);

  // Update gradient name
  const updateName = useCallback((name: string) => {
    setGradient({
      ...gradient,
      name,
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Update gradient configuration
  const updateConfig = useCallback((config: GradientConfig) => {
    setGradient({
      ...gradient,
      config,
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Update gradient type
  const updateGradientType = useCallback((type: GradientType) => {
    let newConfig: GradientConfig;

    switch (type) {
      case 'linear':
        newConfig = { type: 'linear', angle: 90 };
        break;
      case 'radial':
        newConfig = { type: 'radial', shape: 'circle', position: 'center' };
        break;
      case 'conic':
        newConfig = { type: 'conic', startAngle: 0, centerX: 50, centerY: 50 };
        break;
      default:
        newConfig = { type: 'linear', angle: 90 };
    }

    setGradient({
      ...gradient,
      config: newConfig,
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Add new color stop
  const addColorStop = useCallback((position?: number) => {
    const sortedStops = [...gradient.colorStops].sort((a, b) => a.position - b.position);
    const newPosition = position ?? (sortedStops.length > 0 ?
      (sortedStops[sortedStops.length - 1].position + sortedStops[0].position) / 2 : 50);

    const newStop: ColorStop = {
      id: generateId(),
      color: '#ffffff',
      alpha: 1,
      position: newPosition,
      format: 'HEX'
    };

    setGradient({
      ...gradient,
      colorStops: [...gradient.colorStops, newStop],
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Remove color stop
  const removeColorStop = useCallback((stopId: string) => {
    // Don't allow removing if only 2 stops remain
    if (gradient.colorStops.length <= 2) return;

    setGradient({
      ...gradient,
      colorStops: gradient.colorStops.filter(stop => stop.id !== stopId),
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Update color stop
  const updateColorStop = useCallback((stopId: string, updates: Partial<ColorStop>) => {
    setGradient({
      ...gradient,
      colorStops: gradient.colorStops.map(stop =>
        stop.id === stopId ? { ...stop, ...updates } : stop
      ),
      updatedAt: new Date()
    });
  }, [gradient, setGradient]);

  // Update color stop color with format conversion
  const updateColorStopColor = useCallback((stopId: string, color: string, format: ColorFormat) => {
    try {
      const validation = colorUtils.validateColor(color);
      if (!validation.isValid) return;

      const normalizedColor = validation.normalizedColor || color;
      
      setGradient(prev => ({
        ...prev,
        colorStops: prev.colorStops.map(stop => 
          stop.id === stopId ? { 
            ...stop, 
            color: normalizedColor,
            format 
          } : stop
        ),
        updatedAt: new Date()
      }));
    } catch (error) {
      console.warn('Failed to update color:', error);
    }
  }, []);

  // Update color stop position
  const updateColorStopPosition = useCallback((stopId: string, position: number) => {
    const clampedPosition = Math.max(0, Math.min(100, position));
    
    setGradient(prev => ({
      ...prev,
      colorStops: prev.colorStops.map(stop => 
        stop.id === stopId ? { ...stop, position: clampedPosition } : stop
      ),
      updatedAt: new Date()
    }));
  }, []);

  // Update color stop alpha
  const updateColorStopAlpha = useCallback((stopId: string, alpha: number) => {
    const clampedAlpha = Math.max(0, Math.min(1, alpha));
    
    setGradient(prev => ({
      ...prev,
      colorStops: prev.colorStops.map(stop => 
        stop.id === stopId ? { ...stop, alpha: clampedAlpha } : stop
      ),
      updatedAt: new Date()
    }));
  }, []);

  // Reorder color stops (for drag and drop)
  const reorderColorStops = useCallback((startIndex: number, endIndex: number) => {
    setGradient(prev => {
      const newColorStops = [...prev.colorStops];
      const [removed] = newColorStops.splice(startIndex, 1);
      newColorStops.splice(endIndex, 0, removed);
      
      return {
        ...prev,
        colorStops: newColorStops,
        updatedAt: new Date()
      };
    });
  }, []);

  // Reset gradient to default
  const resetGradient = useCallback(() => {
    const newGradient = createDefaultGradient();
    setGradient(newGradient);
    undoRedoActions.clearHistory();
  }, [setGradient, undoRedoActions]);

  // Generate code in all formats
  const generatedCode = useMemo(() => {
    return codeGeneration.generateAllFormats(gradient);
  }, [gradient]);

  // Generate CSS gradient string
  const cssGradient = useMemo(() => {
    return codeGeneration.generateCSSGradient(gradient);
  }, [gradient]);

  // Generate inline style for preview
  const previewStyle = useMemo(() => {
    return codeGeneration.generateInlineStyle(gradient);
  }, [gradient]);

  // Sorted color stops for rendering
  const sortedColorStops = useMemo(() => {
    return [...gradient.colorStops].sort((a, b) => a.position - b.position);
  }, [gradient.colorStops]);

  return {
    // State
    gradient,
    sortedColorStops,
    
    // Actions
    updateName,
    updateConfig,
    updateGradientType,
    addColorStop,
    removeColorStop,
    updateColorStop,
    updateColorStopColor,
    updateColorStopPosition,
    updateColorStopAlpha,
    reorderColorStops,
    resetGradient,
    
    // Computed values
    generatedCode,
    cssGradient,
    previewStyle,
    
    // Utilities
    canRemoveStop: gradient.colorStops.length > 2,
    canAddStop: gradient.colorStops.length < 10,

    // Undo/Redo
    canUndo: undoRedoActions.canUndo,
    canRedo: undoRedoActions.canRedo,
    undo: undoRedoActions.undo,
    redo: undoRedoActions.redo
  };
};
