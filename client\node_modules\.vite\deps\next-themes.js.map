{"version": 3, "sources": ["../../next-themes/dist/index.module.js"], "sourcesContent": ["import e,{useContext as t,Fragment as n,useState as r,use<PERSON><PERSON>back as o,useEffect as a,useMemo as s,memo as l,createContext as m}from\"react\";const c=[\"light\",\"dark\"],i=\"(prefers-color-scheme: dark)\",d=\"undefined\"==typeof window,u=/*#__PURE__*/m(void 0),h={setTheme:e=>{},themes:[]},y=()=>{var e;return null!==(e=t(u))&&void 0!==e?e:h},$=r=>t(u)?/*#__PURE__*/e.createElement(n,null,r.children):/*#__PURE__*/e.createElement(f,r),v=[\"light\",\"dark\"],f=({forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:l=!0,enableColorScheme:m=!0,storageKey:d=\"theme\",themes:h=v,defaultTheme:y=(l?\"system\":\"light\"),attribute:$=\"data-theme\",value:f,children:w,nonce:T})=>{const[E,k]=r(()=>S(d,y)),[C,L]=r(()=>S(d)),x=f?Object.values(f):h,I=o(e=>{let t=e;if(!t)return;\"system\"===e&&l&&(t=p());const r=f?f[t]:t,o=n?b():null,a=document.documentElement;if(\"class\"===$?(a.classList.remove(...x),r&&a.classList.add(r)):r?a.setAttribute($,r):a.removeAttribute($),m){const e=c.includes(y)?y:null,n=c.includes(t)?t:e;a.style.colorScheme=n}null==o||o()},[]),O=o(e=>{k(e);try{localStorage.setItem(d,e)}catch(e){}},[t]),M=o(e=>{const n=p(e);L(n),\"system\"===E&&l&&!t&&I(\"system\")},[E,t]);a(()=>{const e=window.matchMedia(i);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),a(()=>{const e=e=>{e.key===d&&O(e.newValue||y)};return window.addEventListener(\"storage\",e),()=>window.removeEventListener(\"storage\",e)},[O]),a(()=>{I(null!=t?t:E)},[t,E]);const A=s(()=>({theme:E,setTheme:O,forcedTheme:t,resolvedTheme:\"system\"===E?C:E,themes:l?[...h,\"system\"]:h,systemTheme:l?C:void 0}),[E,O,t,C,l,h]);/*#__PURE__*/return e.createElement(u.Provider,{value:A},/*#__PURE__*/e.createElement(g,{forcedTheme:t,disableTransitionOnChange:n,enableSystem:l,enableColorScheme:m,storageKey:d,themes:h,defaultTheme:y,attribute:$,value:f,children:w,attrs:x,nonce:T}),w)},g=/*#__PURE__*/l(({forcedTheme:t,storageKey:n,attribute:r,enableSystem:o,enableColorScheme:a,defaultTheme:s,value:l,attrs:m,nonce:d})=>{const u=\"system\"===s,h=\"class\"===r?`var d=document.documentElement,c=d.classList;c.remove(${m.map(e=>`'${e}'`).join(\",\")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,y=a?c.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:\"if(e==='light'||e==='dark')d.style.colorScheme=e\":\"\",$=(e,t=!1,n=!0)=>{const o=l?l[e]:e,s=t?e+\"|| ''\":`'${o}'`;let m=\"\";return a&&n&&!t&&c.includes(e)&&(m+=`d.style.colorScheme = '${e}';`),\"class\"===r?m+=t||o?`c.add(${s})`:\"null\":o&&(m+=`d[s](n,${s})`),m},v=t?`!function(){${h}${$(t)}}()`:o?`!function(){try{${h}var e=localStorage.getItem('${n}');if('system'===e||(!e&&${u})){var t='${i}',m=window.matchMedia(t);if(m.media!==t||m.matches){${$(\"dark\")}}else{${$(\"light\")}}}else if(e){${l?`var x=${JSON.stringify(l)};`:\"\"}${$(l?\"x[e]\":\"e\",!0)}}${u?\"\":\"else{\"+$(s,!1,!1)+\"}\"}${y}}catch(e){}}()`:`!function(){try{${h}var e=localStorage.getItem('${n}');if(e){${l?`var x=${JSON.stringify(l)};`:\"\"}${$(l?\"x[e]\":\"e\",!0)}}else{${$(s,!1,!1)};}${y}}catch(t){}}();`;/*#__PURE__*/return e.createElement(\"script\",{nonce:d,dangerouslySetInnerHTML:{__html:v}})},()=>!0),S=(e,t)=>{if(d)return;let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},b=()=>{const e=document.createElement(\"style\");return e.appendChild(document.createTextNode(\"*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},p=e=>(e||(e=window.matchMedia(i)),e.matches?\"dark\":\"light\");export{$ as ThemeProvider,y as useTheme};\n"], "mappings": ";;;;;;;;AAAA,mBAAoI;AAAQ,IAAM,IAAE,CAAC,SAAQ,MAAM;AAAvB,IAAyB,IAAE;AAA3B,IAA0D,IAAE,eAAa,OAAO;AAAhF,IAAuF,QAAe,aAAAA,eAAE,MAAM;AAA9G,IAAgH,IAAE,EAAC,UAAS,CAAAC,OAAG;AAAC,GAAE,QAAO,CAAC,EAAC;AAA3I,IAA6I,IAAE,MAAI;AAAC,MAAIA;AAAE,SAAO,UAAQA,SAAE,aAAAC,YAAE,CAAC,MAAI,WAASD,KAAEA,KAAE;AAAC;AAAhM,IAAkM,IAAE,CAAAE,WAAG,aAAAD,YAAE,CAAC,IAAe,aAAAD,QAAE,cAAc,aAAAG,UAAE,MAAKD,GAAE,QAAQ,IAAe,aAAAF,QAAE,cAAc,GAAEE,EAAC;AAA5R,IAA8R,IAAE,CAAC,SAAQ,MAAM;AAA/S,IAAiT,IAAE,CAAC,EAAC,aAAYD,IAAE,2BAA0BE,KAAE,OAAG,cAAaC,KAAE,MAAG,mBAAkBL,KAAE,MAAG,YAAWM,KAAE,SAAQ,QAAOC,KAAE,GAAE,cAAaC,KAAGH,KAAE,WAAS,SAAS,WAAUI,KAAE,cAAa,OAAMC,IAAE,UAAS,GAAE,OAAM,EAAC,MAAI;AAAC,QAAK,CAAC,GAAE,CAAC,QAAE,aAAAP,UAAE,MAAI,EAAEG,IAAEE,EAAC,CAAC,GAAE,CAAC,GAAE,CAAC,QAAE,aAAAL,UAAE,MAAI,EAAEG,EAAC,CAAC,GAAE,IAAEI,KAAE,OAAO,OAAOA,EAAC,IAAEH,IAAE,QAAE,aAAAI,aAAE,CAAAV,OAAG;AAAC,QAAIC,KAAED;AAAE,QAAG,CAACC;AAAE;AAAO,iBAAWD,MAAGI,OAAIH,KAAE,EAAE;AAAG,UAAMC,KAAEO,KAAEA,GAAER,EAAC,IAAEA,IAAES,KAAEP,KAAE,EAAE,IAAE,MAAKQ,KAAE,SAAS;AAAgB,QAAG,YAAUH,MAAGG,GAAE,UAAU,OAAO,GAAG,CAAC,GAAET,MAAGS,GAAE,UAAU,IAAIT,EAAC,KAAGA,KAAES,GAAE,aAAaH,IAAEN,EAAC,IAAES,GAAE,gBAAgBH,EAAC,GAAET,IAAE;AAAC,YAAMC,KAAE,EAAE,SAASO,EAAC,IAAEA,KAAE,MAAKJ,KAAE,EAAE,SAASF,EAAC,IAAEA,KAAED;AAAE,MAAAW,GAAE,MAAM,cAAYR;AAAA,IAAC;AAAC,YAAMO,MAAGA,GAAE;AAAA,EAAC,GAAE,CAAC,CAAC,GAAE,QAAE,aAAAA,aAAE,CAAAV,OAAG;AAAC,MAAEA,EAAC;AAAE,QAAG;AAAC,mBAAa,QAAQK,IAAEL,EAAC;AAAA,IAAC,SAAOA,IAAE;AAAA,IAAC;AAAA,EAAC,GAAE,CAACC,EAAC,CAAC,GAAE,QAAE,aAAAS,aAAE,CAAAV,OAAG;AAAC,UAAMG,KAAE,EAAEH,EAAC;AAAE,MAAEG,EAAC,GAAE,aAAW,KAAGC,MAAG,CAACH,MAAG,EAAE,QAAQ;AAAA,EAAC,GAAE,CAAC,GAAEA,EAAC,CAAC;AAAE,mBAAAU,WAAE,MAAI;AAAC,UAAMX,KAAE,OAAO,WAAW,CAAC;AAAE,WAAOA,GAAE,YAAY,CAAC,GAAE,EAAEA,EAAC,GAAE,MAAIA,GAAE,eAAe,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,OAAE,aAAAW,WAAE,MAAI;AAAC,UAAMX,KAAE,CAAAA,OAAG;AAAC,MAAAA,GAAE,QAAMK,MAAG,EAAEL,GAAE,YAAUO,EAAC;AAAA,IAAC;AAAE,WAAO,OAAO,iBAAiB,WAAUP,EAAC,GAAE,MAAI,OAAO,oBAAoB,WAAUA,EAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,OAAE,aAAAW,WAAE,MAAI;AAAC,MAAE,QAAMV,KAAEA,KAAE,CAAC;AAAA,EAAC,GAAE,CAACA,IAAE,CAAC,CAAC;AAAE,QAAM,QAAE,aAAAW,SAAE,OAAK,EAAC,OAAM,GAAE,UAAS,GAAE,aAAYX,IAAE,eAAc,aAAW,IAAE,IAAE,GAAE,QAAOG,KAAE,CAAC,GAAGE,IAAE,QAAQ,IAAEA,IAAE,aAAYF,KAAE,IAAE,OAAM,IAAG,CAAC,GAAE,GAAEH,IAAE,GAAEG,IAAEE,EAAC,CAAC;AAAe,SAAO,aAAAN,QAAE,cAAc,EAAE,UAAS,EAAC,OAAM,EAAC,GAAe,aAAAA,QAAE,cAAc,GAAE,EAAC,aAAYC,IAAE,2BAA0BE,IAAE,cAAaC,IAAE,mBAAkBL,IAAE,YAAWM,IAAE,QAAOC,IAAE,cAAaC,IAAE,WAAUC,IAAE,OAAMC,IAAE,UAAS,GAAE,OAAM,GAAE,OAAM,EAAC,CAAC,GAAE,CAAC;AAAC;AAA1pD,IAA4pD,QAAe,aAAAL,MAAE,CAAC,EAAC,aAAYH,IAAE,YAAWE,IAAE,WAAUD,IAAE,cAAaQ,IAAE,mBAAkBC,IAAE,cAAaC,IAAE,OAAMR,IAAE,OAAML,IAAE,OAAMM,GAAC,MAAI;AAAC,QAAMQ,KAAE,aAAWD,IAAEN,KAAE,YAAUJ,KAAE,yDAAyDH,GAAE,IAAI,CAAAC,OAAG,IAAIA,EAAC,GAAG,EAAE,KAAK,GAAG,CAAC,OAAK,qCAAqCE,EAAC,uBAAsBK,KAAEI,KAAE,EAAE,SAASC,EAAC,KAAGA,KAAE,0DAA0DA,EAAC,MAAI,qDAAmD,IAAGJ,KAAE,CAACR,IAAEC,KAAE,OAAGE,KAAE,SAAK;AAAC,UAAMO,KAAEN,KAAEA,GAAEJ,EAAC,IAAEA,IAAEY,KAAEX,KAAED,KAAE,UAAQ,IAAIU,EAAC;AAAI,QAAIX,KAAE;AAAG,WAAOY,MAAGR,MAAG,CAACF,MAAG,EAAE,SAASD,EAAC,MAAID,MAAG,0BAA0BC,EAAC,OAAM,YAAUE,KAAEH,MAAGE,MAAGS,KAAE,SAASE,EAAC,MAAI,SAAOF,OAAIX,MAAG,UAAUa,EAAC,MAAKb;AAAA,EAAC,GAAEe,KAAEb,KAAE,eAAeK,EAAC,GAAGE,GAAEP,EAAC,CAAC,QAAMS,KAAE,mBAAmBJ,EAAC,+BAA+BH,EAAC,4BAA4BU,EAAC,aAAa,CAAC,uDAAuDL,GAAE,MAAM,CAAC,SAASA,GAAE,OAAO,CAAC,gBAAgBJ,KAAE,SAAS,KAAK,UAAUA,EAAC,CAAC,MAAI,EAAE,GAAGI,GAAEJ,KAAE,SAAO,KAAI,IAAE,CAAC,IAAIS,KAAE,KAAG,UAAQL,GAAEI,IAAE,OAAG,KAAE,IAAE,GAAG,GAAGL,EAAC,mBAAiB,mBAAmBD,EAAC,+BAA+BH,EAAC,YAAYC,KAAE,SAAS,KAAK,UAAUA,EAAC,CAAC,MAAI,EAAE,GAAGI,GAAEJ,KAAE,SAAO,KAAI,IAAE,CAAC,SAASI,GAAEI,IAAE,OAAG,KAAE,CAAC,KAAKL,EAAC;AAA+B,SAAO,aAAAP,QAAE,cAAc,UAAS,EAAC,OAAMK,IAAE,yBAAwB,EAAC,QAAOS,GAAC,EAAC,CAAC;AAAC,GAAE,MAAI,IAAE;AAAt4F,IAAw4F,IAAE,CAACd,IAAEC,OAAI;AAAC,MAAG;AAAE;AAAO,MAAIE;AAAE,MAAG;AAAC,IAAAA,KAAE,aAAa,QAAQH,EAAC,KAAG;AAAA,EAAM,SAAOA,IAAE;AAAA,EAAC;AAAC,SAAOG,MAAGF;AAAC;AAA/9F,IAAi+F,IAAE,MAAI;AAAC,QAAMD,KAAE,SAAS,cAAc,OAAO;AAAE,SAAOA,GAAE,YAAY,SAAS,eAAe,0JAA0J,CAAC,GAAE,SAAS,KAAK,YAAYA,EAAC,GAAE,MAAI;AAAC,WAAO,iBAAiB,SAAS,IAAI,GAAE,WAAW,MAAI;AAAC,eAAS,KAAK,YAAYA,EAAC;AAAA,IAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAAp1G,IAAs1G,IAAE,CAAAA,QAAIA,OAAIA,KAAE,OAAO,WAAW,CAAC,IAAGA,GAAE,UAAQ,SAAO;", "names": ["m", "e", "t", "r", "n", "l", "d", "h", "y", "$", "f", "o", "a", "s", "u", "v"]}