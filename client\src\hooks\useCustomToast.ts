import { useCallback } from "react";
import { toast } from "@/hooks/use-toast";

type ToastType = 'success' | 'error' | 'info' | 'warning';

export const useCustomToast = () => {
  const showToast = useCallback((title: string, type: ToastType = 'info', description?: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      info: 'default',
      warning: 'default'
    };

    toast({
      title,
      description,
      variant: variants[type] as any,
    });
  }, []);

  return { showToast };
};
