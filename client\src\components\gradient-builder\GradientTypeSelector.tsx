import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Circle, RotateCcw } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { GradientTypeSelectorProps, GradientType } from '@/types/types';
import { cn } from '@/lib/utils';

interface GradientTypeOption {
  type: GradientType;
  label: string;
  description: string;
  icon: React.ReactNode;
  preview: string;
}

const gradientTypeOptions: GradientTypeOption[] = [
  {
    type: 'linear',
    label: 'Linear',
    description: 'Straight line transition',
    icon: <ArrowRight className="h-4 w-4" />,
    preview: 'linear-gradient(90deg, #ff6b6b 0%, #4ecdc4 100%)'
  },
  {
    type: 'radial',
    label: 'Radial',
    description: 'Circular transition',
    icon: <Circle className="h-4 w-4" />,
    preview: 'radial-gradient(circle at center, #ff6b6b 0%, #4ecdc4 100%)'
  },
  {
    type: 'conic',
    label: 'Conic',
    description: 'Rotational transition',
    icon: <RotateCcw className="h-4 w-4" />,
    preview: 'conic-gradient(from 0deg at 50% 50%, #ff6b6b 0%, #4ecdc4 100%)'
  }
];

export const GradientTypeSelector: React.FC<GradientTypeSelectorProps> = ({
  gradientType,
  onGradientTypeChange,
  showIcons = true
}) => {
  return (
    <div className="space-y-3" role="group" aria-labelledby="gradient-type-label">
      <Label id="gradient-type-label" className="text-sm font-medium">Gradient Type</Label>
      
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        {gradientTypeOptions.map((option) => (
          <motion.button
            key={option.type}
            type="button"
            onClick={() => onGradientTypeChange(option.type)}
            className={cn(
              "relative p-4 rounded-lg border-2 transition-all duration-200 text-left",
              "hover:border-primary/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
              gradientType === option.type
                ? "border-primary bg-primary/5"
                : "border-border bg-card hover:bg-accent/50"
            )}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            aria-pressed={gradientType === option.type}
            aria-describedby={`${option.type}-description`}
            role="radio"
            tabIndex={0}
          >
            {/* Preview Background */}
            <div
              className="absolute inset-0 rounded-lg opacity-10"
              style={{ background: option.preview }}
            />
            
            {/* Content */}
            <div className="relative z-10">
              {/* Header */}
              <div className="flex items-center gap-2 mb-2">
                {showIcons && (
                  <div className={cn(
                    "p-1.5 rounded-md",
                    gradientType === option.type
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}>
                    {option.icon}
                  </div>
                )}
                <div>
                  <div className={cn(
                    "font-medium text-sm",
                    gradientType === option.type
                      ? "text-primary"
                      : "text-foreground"
                  )}>
                    {option.label}
                  </div>
                </div>
              </div>
              
              {/* Description */}
              <p id={`${option.type}-description`} className="text-xs text-muted-foreground mb-3">
                {option.description}
              </p>
              
              {/* Visual Preview */}
              <div
                className="h-8 w-full rounded border border-border/50"
                style={{ background: option.preview }}
              />
            </div>
            
            {/* Selection Indicator */}
            {gradientType === option.type && (
              <motion.div
                className="absolute top-2 right-2 w-2 h-2 bg-primary rounded-full"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              />
            )}
          </motion.button>
        ))}
      </div>
      
      {/* Additional Info */}
      <div className="text-xs text-muted-foreground">
        {gradientType === 'linear' && (
          <p>Linear gradients transition colors along a straight line. Adjust the angle to change direction.</p>
        )}
        {gradientType === 'radial' && (
          <p>Radial gradients transition colors from a center point outward. Choose between circle and ellipse shapes.</p>
        )}
        {gradientType === 'conic' && (
          <p>Conic gradients rotate colors around a center point. Adjust the start angle and center position.</p>
        )}
      </div>
    </div>
  );
};
