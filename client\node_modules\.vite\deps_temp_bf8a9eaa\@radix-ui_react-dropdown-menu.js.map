{"version": 3, "sources": ["../../@radix-ui/primitive/dist/packages/core/primitive/src/index.ts", "../../@radix-ui/primitive/dist/packages/core/primitive/src/primitive.tsx", "../../@radix-ui/react-context/dist/packages/react/context/src/index.ts", "../../@radix-ui/react-context/dist/packages/react/context/src/createContext.tsx", "../../@radix-ui/react-use-callback-ref/dist/packages/react/use-callback-ref/src/index.ts", "../../@radix-ui/react-use-callback-ref/dist/packages/react/use-callback-ref/src/useCallbackRef.tsx", "../../@radix-ui/react-use-controllable-state/dist/packages/react/use-controllable-state/src/index.ts", "../../@radix-ui/react-use-controllable-state/dist/packages/react/use-controllable-state/src/useControllableState.tsx", "../../@radix-ui/react-primitive/dist/packages/react/primitive/src/index.ts", "../../@radix-ui/react-primitive/dist/packages/react/primitive/src/Primitive.tsx", "../../@radix-ui/react-collection/dist/packages/react/collection/src/index.ts", "../../@radix-ui/react-collection/dist/packages/react/collection/src/Collection.tsx", "../../@radix-ui/react-direction/dist/packages/react/direction/src/index.ts", "../../@radix-ui/react-direction/dist/packages/react/direction/src/Direction.tsx", "../../@radix-ui/react-use-escape-keydown/dist/packages/react/use-escape-keydown/src/index.ts", "../../@radix-ui/react-use-escape-keydown/dist/packages/react/use-escape-keydown/src/useEscapeKeydown.tsx", "../../@radix-ui/react-dismissable-layer/dist/packages/react/dismissable-layer/src/index.ts", "../../@radix-ui/react-dismissable-layer/dist/packages/react/dismissable-layer/src/DismissableLayer.tsx", "../../@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/index.ts", "../../@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/FocusGuards.tsx", "../../@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/index.ts", "../../@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/FocusScope.tsx", "../../@radix-ui/react-use-layout-effect/dist/packages/react/use-layout-effect/src/index.ts", "../../@radix-ui/react-use-layout-effect/dist/packages/react/use-layout-effect/src/useLayoutEffect.tsx", "../../@radix-ui/react-id/dist/packages/react/id/src/index.ts", "../../@radix-ui/react-id/dist/packages/react/id/src/id.tsx", "../../@radix-ui/react-arrow/dist/packages/react/arrow/src/index.ts", "../../@radix-ui/react-arrow/dist/packages/react/arrow/src/Arrow.tsx", "../../@radix-ui/react-use-size/dist/packages/react/use-size/src/index.ts", "../../@radix-ui/react-use-size/dist/packages/react/use-size/src/useSize.tsx", "../../@radix-ui/react-popper/dist/packages/react/popper/src/index.ts", "../../@radix-ui/react-popper/dist/packages/react/popper/src/Popper.tsx", "../../@radix-ui/react-portal/dist/packages/react/portal/src/index.ts", "../../@radix-ui/react-portal/dist/packages/react/portal/src/Portal.tsx", "../../@radix-ui/react-presence/dist/packages/react/presence/src/index.ts", "../../@radix-ui/react-presence/dist/packages/react/presence/src/Presence.tsx", "../../@radix-ui/react-presence/dist/packages/react/presence/src/useStateMachine.tsx", "../../@radix-ui/react-roving-focus/dist/packages/react/roving-focus/src/index.ts", "../../@radix-ui/react-roving-focus/dist/packages/react/roving-focus/src/RovingFocusGroup.tsx", "../../react-remove-scroll/dist/es2015/Combination.js", "../../react-remove-scroll/dist/es2015/UI.js", "../../react-remove-scroll/dist/es2015/medium.js", "../../react-remove-scroll/dist/es2015/SideEffect.js", "../../react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../react-remove-scroll/dist/es2015/handleScroll.js", "../../react-remove-scroll/dist/es2015/sidecar.js", "../../@radix-ui/react-menu/dist/packages/react/menu/src/index.ts", "../../@radix-ui/react-menu/dist/packages/react/menu/src/Menu.tsx", "../../@radix-ui/react-dropdown-menu/dist/packages/react/dropdown-menu/src/index.ts", "../../@radix-ui/react-dropdown-menu/dist/packages/react/dropdown-menu/src/DropdownMenu.tsx"], "sourcesContent": ["export { composeEventHandlers } from './primitive';\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n", "export { createContext, createContextScope } from './createContext';\nexport type { CreateScope, Scope } from './createContext';\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n", "export { useCallbackRef } from './useCallbackRef';\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n", "export { useControllableState } from './useControllableState';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n", "export {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n} from './Primitive';\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef } from './Primitive';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n", "export { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\ntype SlotProps = Radix.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "export {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n} from './Direction';\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n", "export { useEscapeKeydown } from './useEscapeKeydown';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n", "export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "export { useLayoutEffect } from './useLayoutEffect';\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n", "export { useId } from './id';\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We `toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)['useId'.toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "export {\n  Arrow,\n  //\n  Root,\n} from './Arrow';\nexport type { ArrowProps } from './Arrow';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ElementRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = Radix.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n", "export { useSize } from './useSize';\n", "/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n", "export {\n  createPopperScope,\n  //\n  <PERSON><PERSON>,\n  <PERSON>perAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n} from './Popper';\nexport type {\n  PopperProps,\n  PopperAnchorProps,\n  PopperContentProps,\n  PopperArrowProps,\n} from './Popper';\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n              // hide the content if using the hide middleware and should be hidden\n              opacity: middlewareData.hide?.referenceHidden ? 0 : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = Radix.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "export {\n  Portal,\n  //\n  Root,\n} from './Portal';\nexport type { PortalProps } from './Portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: HTMLElement | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container = globalThis?.document?.body, ...portalProps } = props;\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "export { Presence } from './Presence';\nexport type { PresenceProps } from './Presence';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './useStateMachine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement;\n\n  const ref = useComposedRefs(presence.ref, (child as any).ref);\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration>({} as any);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied\n          // a frame after the animation ends, creating a flash of visible content.\n          // By manually flushing we ensure they sync within a frame, removing the flash.\n          ReactDOM.flushSync(() => send('ANIMATION_END'));\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      if (node) stylesRef.current = getComputedStyle(node);\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles?: CSSStyleDeclaration) {\n  return styles?.animationName || 'none';\n}\n\nexport { Presence };\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n", "export {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n} from './RovingFocusGroup';\nexport type { RovingFocusGroupProps, RovingFocusItemProps } from './RovingFocusGroup';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId = null, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId,\n    onChange: onCurrentTabStopIdChange,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends PrimitiveSpanProps {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(function () { return styleSingleton(); })[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if ('touches' in event && event.touches.length === 2) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && e.target === event.target && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: \"margin\" }) : null));\n}\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];\n            if (s > d) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== document.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        target = target.parentNode;\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    if (isDeltaPositive && ((noOverscroll && availableScroll === 0) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && availableScrollTop === 0) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "export {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n} from './Menu';\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n} from './Menu';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ElementRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = Radix.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (beacuse of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus();\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus();\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ElementRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ElementRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n", "export {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n} from './DropdownMenu';\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n} from './DropdownMenu';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ElementRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ElementRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ElementRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ElementRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ElementRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ElementRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ElementRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ElementRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ElementRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ElementRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = Radix.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA,SAASA,0CACPC,sBACAC,iBACA,EAAA,2BAA6B,KAA3BC,IAAoC,CAAA,GACtC;AACA,SAAO,SAASC,YAAYC,OAAU;AACpCJ,6BAAoB,QAApBA,yBAAoB,UAApBA,qBAAuBI,KAAH;AAEpB,QAAIF,6BAA6B,SAAS,CAAGE,MAA4BC;AACvE,aAAOJ,oBAAP,QAAOA,oBAAP,SAAA,SAAOA,gBAAkBG,KAAH;;;;;;AE8B5B,SAASE,yCAAmBC,WAAmBC,yBAAwC,CAAA,GAAI;AACzF,MAAIC,kBAAyB,CAAA;AAM7B,WAASC,0CACPC,mBACAC,gBACA;AACA,UAAMC,kBAAcC,aAAAA,eAAkDF,cAAlD;AACpB,UAAMG,QAAQN,gBAAgBO;AAC9BP,sBAAkB;SAAIA;MAAiBG;;AAEvC,aAASK,SACPC,OACA;AACA,YAAM,EAzDZ,OAAA,UAyD+B,GAAGC,QAAH,IAAeD;AACxC,YAAME,WAAUC,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQd,SAAH,EAAcQ,KAAnB,MAA6BF;AAG7C,YAAMS,YAAQR,aAAAA;QAAc,MAAMK;QAASI,OAAOC,OAAOL,OAAd;MAA7B;AACd,iBAAO,aAAAM,eAAC,QAAQ,UAAhB;QAAyB;SAAeC,QAAjC;;AAGT,aAASC,WAAWC,cAAsBP,OAA4C;AACpF,YAAMD,WAAUC,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQd,SAAH,EAAcQ,KAAnB,MAA6BF;AAC7C,YAAMM,cAAUL,aAAAA,YAAiBM,OAAjB;AAChB,UAAID;AAAS,eAAOA;AACpB,UAAIP,mBAAmBiB;AAAW,eAAOjB;AAEzC,YAAM,IAAIkB,MAAO,KAAIF,YAAa,4BAA2BjB,iBAAkB,IAAzE;;AAGRM,aAASc,cAAcpB,oBAAoB;AAC3C,WAAO;MAACM;MAAUU;;;AAOpB,QAAMK,cAA2B,MAAM;AACrC,UAAMC,gBAAgBxB,gBAAgByB,IAAKtB,CAAAA,mBAAmB;AAC5D,iBAAOE,aAAAA,eAAoBF,cAApB;KADa;AAGtB,WAAO,SAASuB,SAASd,OAAc;AACrC,YAAMe,YAAWf,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQd,SAAH,MAAiB0B;AACvC,iBAAOnB,aAAAA;QACL,OAAO;UAAE,CAAE,UAASP,SAAU,EAArB,GAAyB;YAAE,GAAGc;YAAO,CAACd,SAAD,GAAa6B;;;QAC3D;UAACf;UAAOe;;MAFH;;;AAOXJ,cAAYzB,YAAYA;AACxB,SAAO;IAACG;IAAe2B,2CAAqBL,aAAD,GAAiBxB,sBAAjB;;;AAO7C,SAAS6B,8CAAwBC,QAAuB;AACtD,QAAMC,YAAYD,OAAO,CAAD;AACxB,MAAIA,OAAOtB,WAAW;AAAG,WAAOuB;AAEhC,QAAMP,eAA2B,MAAM;AACrC,UAAMQ,aAAaF,OAAOJ;MAAKF,CAAAA,iBAAiB;QAC9CG,UAAUH,YAAW;QACrBzB,WAAWyB,YAAYzB;;IAFN;AAKnB,WAAO,SAASkC,kBAAkBC,gBAAgB;AAChD,YAAMC,cAAaH,WAAWI,OAAO,CAACD,YAAY,EAlHxD,UAAA,UAkHoEpC,MAAgB;AAI5E,cAAMsC,aAAaV,SAASO,cAAD;AAC3B,cAAMI,eAAeD,WAAY,UAAStC,SAAU,EAArB;AAC/B,eAAO;UAAE,GAAGoC;UAAY,GAAGG;;SAC1B,CAAA,CAPgB;AASnB,iBAAOhC,aAAAA;QAAc,OAAO;UAAE,CAAE,UAASyB,UAAUhC,SAAU,EAA/B,GAAmCoC;;QAAe;UAACA;;MAA1E;;;AAIXX,eAAYzB,YAAYgC,UAAUhC;AAClC,SAAOyB;;;;;;;;AE1HT,SAASe,0CAAkDC,UAA4B;AACrF,QAAMC,kBAAcC,cAAAA,QAAaF,QAAb;AAEpBE,oBAAAA,WAAgB,MAAM;AACpBD,gBAAYE,UAAUH;GADxB;AAKA,aAAOE,cAAAA;IAAc,MAAO,IAAIE,SAAhC;AAA4B,UAAA;AAAA,cAAA,uBAAaH,YAAYE,aAAzB,QAAA,yBAAA,SAAA,SAAa,qBAAA,KAAAF,aAAW,GAAcG,IAAd;;IAA2B,CAAA;EAAxE;;;;AEHT,SAASC,yCAAwB,EAXjC,MAAA,aAWiC,WAGpB,MAAM;AAAA,EAAjBC,GACgC;AAChC,QAAM,CAACC,kBAAkBC,mBAAnB,IAA0CC,2CAAqB;IAhBvE;IAAA;GAgBsE;AACpE,QAAMC,eAAeC,SAASC;AAC9B,QAAMC,SAAQH,eAAeC,OAAOJ;AACpC,QAAMO,eAAeC,0CAAeT,QAAD;AAEnC,QAAMU,eAAgEC,cAAAA,aACnEC,CAAAA,cAAc;AACb,QAAIR,cAAc;AAChB,YAAMS,SAASD;AACf,YAAML,QAAQ,OAAOK,cAAc,aAAaC,OAAOR,IAAD,IAASO;AAC/D,UAAIL,UAAUF;AAAMG,qBAAaD,KAAD;;AAEhCL,0BAAoBU,SAAD;KAGvB;IAACR;IAAcC;IAAMH;IAAqBM;GAV0B;AAatE,SAAO;IAACD;IAAOG;;;AAGjB,SAASP,2CAAwB,EArCjC,aAAA,SAuCEH,GAC8C;AAC9C,QAAMc,wBAAoBH,cAAAA,UAA8BI,WAA9B;AAC1B,QAAM,CAACR,KAAD,IAAUO;AAChB,QAAME,mBAAeL,cAAAA,QAAaJ,KAAb;AACrB,QAAMC,eAAeC,0CAAeT,QAAD;AAEnCW,oBAAAA,WAAgB,MAAM;AACpB,QAAIK,aAAaC,YAAYV,OAAO;AAClCC,mBAAaD,KAAD;AACZS,mBAAaC,UAAUV;;KAExB;IAACA;IAAOS;IAAcR;GALzB;AAOA,SAAOM;;;;;;AEjDT,IAAMI,8BAAQ;EACZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAuBF,IAAMC,4CAAYD,4BAAME,OAAO,CAACC,WAAWC,SAAS;AAClD,QAAMC,WAAOC,cAAAA,YAAiB,CAACC,OAA2CC,iBAAsB;AAC9F,UAAM,EAAA,SAAW,GAAGC,eAAH,IAAsBF;AACvC,UAAMG,OAAYC,UAAUC,4CAAOR;AAEnCE,sBAAAA,WAAgB,MAAM;AACnBO,aAAeC,OAAOC,IAAI,UAAX,CAAhB,IAA0C;OACzC,CAAA,CAFH;AAIA,eAAO,cAAAC,eAAC,MAAD,SAAA,CAAA,GAAUP,gBAAjB;MAAiC,KAAKD;KAA/B,CAAA;GARI;AAWbH,OAAKY,cAAe,aAAYb,IAAK;AAErC,SAAO;IAAE,GAAGD;IAAW,CAACC,IAAD,GAAQC;;GAC9B,CAAA,CAfe;AA0DlB,SAASa,0CAAmDC,QAAqBC,OAAU;AACzF,MAAID;AAAQE,yBAAAA;MAAmB,MAAMF,OAAOG,cAAcF,KAArB;IAAzB;;;;;;;;AEpFd,SAASG,0CAAiEC,MAAc;AAKtF,QAAMC,gBAAgBD,OAAO;AAC7B,QAAM,CAACE,yBAAyBC,qBAA1B,IAAmDC,yCAAmBH,aAAD;AAO3E,QAAM,CAACI,wBAAwBC,oBAAzB,IAAiDJ,wBACrDD,eACA;IAAEM,eAAe;MAAEC,SAAS;;IAAQC,SAAS,oBAAIC,IAAJ;GAF+B;AAK9E,QAAMC,qBAA4EC,CAAAA,UAAU;AAC1F,UAAM,EArCV,OAAA,SAqCmBC,IAAaD;AAC5B,UAAME,MAAMC,cAAAA,QAAMC,OAA0B,IAAhC;AACZ,UAAMP,UAAUM,cAAAA,QAAMC,OAAgC,oBAAIN,IAAJ,CAAtC,EAAiDF;AACjE,WACE,cAAAS,QAAA,cAAC,wBADH;MAC0B;MAAc;MAAkB,eAAeH;OACpED,QADH;;AAMJ,SAAA,OAAA,oBAAA;IAAA,aAAA;GAAA;AAMA,QAAMK,uBAAuBlB,OAAO;AAEpC,QAAMmB,iBAAiBJ,cAAAA,QAAMK,WAC3B,CAACR,OAAOS,iBAAiB;AACvB,UAAM,EAzDZ,OAAA,SAyDqBR,IAAaD;AAC5B,UAAMU,UAAUhB,qBAAqBY,sBAAsBK,KAAvB;AACpC,UAAMC,eAAeC,0CAAgBJ,cAAcC,QAAQf,aAAvB;AACpC,WAAO,cAAAU,QAAA,cAAC,2CAAR;MAAa,KAAKO;OAAeX,QAA1B;GALY;AASvB,SAAA,OAAA,gBAAA;IAAA,aAAA;GAAA;AAMA,QAAMa,iBAAiB1B,OAAO;AAC9B,QAAM2B,iBAAiB;AAOvB,QAAMC,qBAAqBb,cAAAA,QAAMK,WAC/B,CAACR,OAAOS,iBAAiB;AACvB,UAAM,EAhFZ,OAAA,UAgF+B,GAAGQ,SAAH,IAAgBjB;AACzC,UAAME,MAAMC,cAAAA,QAAMC,OAAoB,IAA1B;AACZ,UAAMQ,eAAeC,0CAAgBJ,cAAcP,GAAf;AACpC,UAAMQ,UAAUhB,qBAAqBoB,gBAAgBH,KAAjB;AAEpCR,kBAAAA,QAAMe,UAAU,MAAM;AACpBR,cAAQb,QAAQsB,IAAIjB,KAAK;QAtFjC;QAsFwC,GAAIe;OAApC;AACA,aAAO,MAAM,KAAKP,QAAQb,QAAQuB,OAAOlB,GAAvB;KAFpB;AAKA,WACE,cAAAG,QAAA,cAAC,2CADH;MACc,CAACU,cAAD,GAAkB;MAAM,KAAKH;OACtCX,QADH;GAbqB;AAoB3B,SAAA,OAAA,oBAAA;IAAA,aAAA;GAAA;AAMA,WAASoB,cAAcV,OAAY;AACjC,UAAMD,UAAUhB,qBAAqBN,OAAO,sBAAsBuB,KAA9B;AAEpC,UAAMW,WAAWnB,cAAAA,QAAMoB,YAAY,MAAM;AACvC,YAAMC,iBAAiBd,QAAQf,cAAcC;AAC7C,UAAI,CAAC4B;AAAgB,eAAO,CAAA;AAC5B,YAAMC,eAAeC,MAAMC,KAAKH,eAAeI,iBAAkB,IAAGb,cAAe,GAAnD,CAAX;AACrB,YAAMc,QAAQH,MAAMC,KAAKjB,QAAQb,QAAQiC,OAAhB,CAAX;AACd,YAAMC,eAAeF,MAAMG;QACzB,CAACC,GAAGC,MAAMT,aAAaU,QAAQF,EAAE/B,IAAIN,OAA3B,IAAuC6B,aAAaU,QAAQD,EAAEhC,IAAIN,OAA3B;MAD9B;AAGrB,aAAOmC;OACN;MAACrB,QAAQf;MAAee,QAAQb;KATlB;AAWjB,WAAOyB;;AAGT,SAAO;IACL;MAAEc,UAAUrC;MAAoBsC,MAAM9B;MAAgB+B,UAAUtB;;IAChEK;IACA9B;;;;;;AEzHJ,IAAMgD,6CAAmBC,cAAAA,eAA2CC,MAA3C;AAiBzB,SAASC,0CAAaC,UAAsB;AAC1C,QAAMC,gBAAYC,cAAAA,YAAiBC,sCAAjB;AAClB,SAAOH,YAAYC,aAAa;;;;;;;;AEhBlC,SAASG,0CACPC,qBACAC,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMC,kBAAkBC,0CAAeL,mBAAD;AAEtCM,oBAAAA,WAAgB,MAAM;AACpB,UAAMC,gBAAiBC,CAAAA,UAAyB;AAC9C,UAAIA,MAAMC,QAAQ;AAChBL,wBAAgBI,KAAD;;AAGnBP,kBAAcS,iBAAiB,WAAWH,aAA1C;AACA,WAAO,MAAMN,cAAcU,oBAAoB,WAAWJ,aAA7C;KACZ;IAACH;IAAiBH;GARrB;;;;AECF,IAAMW,+CAAyB;AAC/B,IAAMC,uCAAiB;AACvB,IAAMC,6CAAuB;AAC7B,IAAMC,sCAAgB;AAEtB,IAAIC;AAEJ,IAAMC,oDAA0BC,cAAAA,eAAoB;EAClDC,QAAQ,oBAAIC,IAAJ;EACRC,wCAAwC,oBAAID,IAAJ;EACxCE,UAAU,oBAAIF,IAAJ;CAHoB;AA0ChC,IAAMG,gDAAmBL,cAAAA,YACvB,CAACM,OAAOC,iBAAiB;AAAA,MAAA;AACvB,QAAM,EAAA,8BAC0B,OAD1B,iBAAA,sBAAA,gBAAA,mBAAA,WAOJ,GAAGC,WAAH,IACEF;AACJ,QAAMG,cAAUT,cAAAA,YAAiBD,6CAAjB;AAChB,QAAM,CAACW,OAAMC,OAAP,QAAkBX,cAAAA,UAA+C,IAA/C;AACxB,QAAMY,iBAAa,sBAAGF,UAAH,QAAGA,UAAH,SAAA,SAAGA,MAAME,mBAAT,QAAA,wBAAA,SAAA,sBAA0BC,eAA1B,QAA0BA,eAA1B,SAAA,SAA0BA,WAAYC;AACzD,QAAM,CAAA,EAAGC,KAAH,QAAYf,cAAAA,UAAe,CAAA,CAAf;AAClB,QAAMgB,eAAeC;IAAgBV;IAAeG,CAAAA,SAASC,QAAQD,IAAD;EAAhC;AACpC,QAAMT,SAASiB,MAAMC,KAAKV,QAAQR,MAAnB;AACf,QAAM,CAACmB,4CAAD,IAAiD;OAAIX,QAAQN;IAAwCkB,MAAM,EAA1D;AACvD,QAAMC,oDAAoDrB,OAAOsB,QAAQH,4CAAf;AAC1D,QAAMI,QAAQd,QAAOT,OAAOsB,QAAQb,KAAf,IAAuB;AAC5C,QAAMe,8BAA8BhB,QAAQN,uCAAuCuB,OAAO;AAC1F,QAAMC,yBAAyBH,SAASF;AAExC,QAAMM,qBAAqBC,4CAAuBC,CAAAA,UAAU;AAC1D,UAAMC,SAASD,MAAMC;AACrB,UAAMC,wBAAwB;SAAIvB,QAAQL;MAAU6B;MAAMC,CAAAA,WAAWA,OAAOC,SAASJ,MAAhB;IAAvC;AAC9B,QAAI,CAACJ,0BAA0BK;AAAuB;AACtDI,6BAAoB,QAApBA,yBAAoB,UAApBA,qBAAuBN,KAAH;AACpBO,0BAAiB,QAAjBA,sBAAiB,UAAjBA,kBAAoBP,KAAH;AACjB,QAAI,CAACA,MAAMQ;AAAkBC,oBAAS,QAATA,cAAS,UAATA,UAAS;KACrC3B,aAP6C;AAShD,QAAM4B,eAAeC,sCAAiBX,CAAAA,UAAU;AAC9C,UAAMC,SAASD,MAAMC;AACrB,UAAMW,kBAAkB;SAAIjC,QAAQL;MAAU6B;MAAMC,CAAAA,WAAWA,OAAOC,SAASJ,MAAhB;IAAvC;AACxB,QAAIW;AAAiB;AACrBC,uBAAc,QAAdA,mBAAc,UAAdA,eAAiBb,KAAH;AACdO,0BAAiB,QAAjBA,sBAAiB,UAAjBA,kBAAoBP,KAAH;AACjB,QAAI,CAACA,MAAMQ;AAAkBC,oBAAS,QAATA,cAAS,UAATA,UAAS;KACrC3B,aAPiC;AASpCgC,4CAAkBd,CAAAA,UAAU;AAC1B,UAAMe,iBAAiBrB,UAAUf,QAAQR,OAAOyB,OAAO;AACvD,QAAI,CAACmB;AAAgB;AACrBC,wBAAe,QAAfA,oBAAe,UAAfA,gBAAkBhB,KAAH;AACf,QAAI,CAACA,MAAMQ,oBAAoBC,WAAW;AACxCT,YAAMiB,eAAN;AACAR,gBAAS;;KAEV3B,aARa;AAUhBZ,oBAAAA,WAAgB,MAAM;AACpB,QAAI,CAACU;AAAM;AACX,QAAIsC,6BAA6B;AAC/B,UAAIvC,QAAQN,uCAAuCuB,SAAS,GAAG;AAC7D5B,0DAA4Bc,cAAcqC,KAAKC,MAAMC;AACrDvC,sBAAcqC,KAAKC,MAAMC,gBAAgB;;AAE3C1C,cAAQN,uCAAuCiD,IAAI1C,KAAnD;;AAEFD,YAAQR,OAAOmD,IAAI1C,KAAnB;AACA2C,yCAAc;AACd,WAAO,MAAM;AACX,UACEL,+BACAvC,QAAQN,uCAAuCuB,SAAS;AAExDd,sBAAcqC,KAAKC,MAAMC,gBAAgBrD;;KAG5C;IAACY;IAAME;IAAeoC;IAA6BvC;GAnBtD;AA2BAT,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAM;AACX,UAAI,CAACU;AAAM;AACXD,cAAQR,OAAOqD,OAAO5C,KAAtB;AACAD,cAAQN,uCAAuCmD,OAAO5C,KAAtD;AACA2C,2CAAc;;KAEf;IAAC3C;IAAMD;GAPV;AASAT,oBAAAA,WAAgB,MAAM;AACpB,UAAMuD,eAAe,MAAMxC,MAAM,CAAA,CAAD;AAChCD,aAAS0C,iBAAiB7D,sCAAgB4D,YAA1C;AACA,WAAO,MAAMzC,SAAS2C,oBAAoB9D,sCAAgB4D,YAA7C;KACZ,CAAA,CAJH;AAMA,aACE,cAAAG,eAAC,0CAAU,KAAX,SAAA,CAAA,GACMlD,YAFR;IAGI,KAAKQ;IACL,OAAO;MACLmC,eAAe1B,8BACXE,yBACE,SACA,SACFgC;MACJ,GAAGrD,MAAM4C;;IAEX,gBAAgBU,0CAAqBtD,MAAMuD,gBAAgBrB,aAAaqB,cAApC;IACpC,eAAeD,0CAAqBtD,MAAMwD,eAAetB,aAAasB,aAAnC;IACnC,sBAAsBF,0CACpBtD,MAAMyD,sBACNnC,mBAAmBmC,oBAFqB;GAb5C,CAAA;CA9FmB;AAoHzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,oCAAc;AAKpB,IAAMC,gDAAyBjE,cAAAA,YAG7B,CAACM,OAAOC,iBAAiB;AACzB,QAAME,cAAUT,cAAAA,YAAiBD,6CAAjB;AAChB,QAAMmE,UAAMlE,cAAAA,QAA4C,IAA5C;AACZ,QAAMgB,eAAeC,0CAAgBV,cAAc2D,GAAf;AAEpClE,oBAAAA,WAAgB,MAAM;AACpB,UAAMU,OAAOwD,IAAIC;AACjB,QAAIzD,MAAM;AACRD,cAAQL,SAASgD,IAAI1C,IAArB;AACA,aAAO,MAAM;AACXD,gBAAQL,SAASkD,OAAO5C,IAAxB;;;KAGH;IAACD,QAAQL;GARZ;AAUA,aAAO,cAAAsD,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBpD,OAA1B;IAAiC,KAAKU;GAA/B,CAAA;CAlBsB;AAqB/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAYA,SAASa,4CACPO,sBACAxB,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMsD,2BAA2BC,0CAAejC,oBAAD;AAC/C,QAAMkC,kCAA8BtE,cAAAA,QAAa,KAAb;AACpC,QAAMuE,qBAAiBvE,cAAAA,QAAa,MAAM;EAAA,CAAnB;AAEvBA,oBAAAA,WAAgB,MAAM;AACpB,UAAMwE,oBAAqB1C,CAAAA,UAAwB;AACjD,UAAIA,MAAMC,UAAU,CAACuC,4BAA4BH,SAAS;AAGxD,YAASM,2CAAT,WAAoD;AAClDC,6DACE9E,4CACAwE,0BACAO,aACA;YAAEC,UAAU;WAJc;;AAH9B,cAAMD,cAAc;UAAEE,eAAe/C;;AAuBrC,YAAIA,MAAMgD,gBAAgB,SAAS;AACjClE,wBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;AACAI,yBAAeJ,UAAUM;AACzB7D,wBAAc4C,iBAAiB,SAASe,eAAeJ,SAAS;YAAEY,MAAM;WAAxE;;AAEAN,mDAAwC;;AAK1C7D,sBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;AAEFG,kCAA4BH,UAAU;;AAexC,UAAMa,UAAUC,OAAOC,WAAW,MAAM;AACtCtE,oBAAc4C,iBAAiB,eAAegB,iBAA9C;OACC,CAFa;AAGhB,WAAO,MAAM;AACXS,aAAOE,aAAaH,OAApB;AACApE,oBAAc6C,oBAAoB,eAAee,iBAAjD;AACA5D,oBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;;KAED;IAACvD;IAAewD;GA7DnB;AA+DA,SAAO;;IAELL,sBAAsB,MAAOO,4BAA4BH,UAAU;;;AAQvE,SAAS1B,sCACPE,gBACA/B,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMsE,qBAAqBf,0CAAe1B,cAAD;AACzC,QAAM0C,gCAA4BrF,cAAAA,QAAa,KAAb;AAElCA,oBAAAA,WAAgB,MAAM;AACpB,UAAMsF,cAAexD,CAAAA,UAAsB;AACzC,UAAIA,MAAMC,UAAU,CAACsD,0BAA0BlB,SAAS;AACtD,cAAMQ,cAAc;UAAEE,eAAe/C;;AACrC4C,2DAA6B7E,qCAAeuF,oBAAoBT,aAAa;UAC3EC,UAAU;SADgB;;;AAKhChE,kBAAc4C,iBAAiB,WAAW8B,WAA1C;AACA,WAAO,MAAM1E,cAAc6C,oBAAoB,WAAW6B,WAA7C;KACZ;IAAC1E;IAAewE;GAXnB;AAaA,SAAO;IACLvB,gBAAgB,MAAOwB,0BAA0BlB,UAAU;IAC3DL,eAAe,MAAOuB,0BAA0BlB,UAAU;;;AAI9D,SAASd,uCAAiB;AACxB,QAAMvB,QAAQ,IAAIyD,YAAY5F,oCAAhB;AACdmB,WAAS0E,cAAc1D,KAAvB;;AAGF,SAAS4C,mDACPe,MACAC,SACAC,QACA,EAAA,SAAEf,GACF;AACA,QAAM7C,SAAS4D,OAAOd,cAAc9C;AACpC,QAAMD,QAAQ,IAAIyD,YAAYE,MAAM;IAAEG,SAAS;IAAOC,YAAY;;GAApD;AACd,MAAIH;AAAS3D,WAAOyB,iBAAiBiC,MAAMC,SAA0B;MAAEX,MAAM;KAAhE;AAEb,MAAIH;AACFkB,8CAA4B/D,QAAQD,KAAT;;AAE3BC,WAAOyD,cAAc1D,KAArB;;;;;AExVJ,IAAIiE,8BAAQ;AAWZ,SAASC,4CAAiB;AACxBC,oBAAAA,WAAgB,MAAM;AAAA,QAAA,cAAA;AACpB,UAAMC,aAAaC,SAASC,iBAAiB,0BAA1B;AACnBD,aAASE,KAAKC,sBAAsB,eAApC,eAAkDJ,WAAW,CAAD,OAA5D,QAAA,iBAAA,SAAA,eAAmEK,uCAAgB,CAAnF;AACAJ,aAASE,KAAKC,sBAAsB,cAApC,gBAAiDJ,WAAW,CAAD,OAA3D,QAAA,kBAAA,SAAA,gBAAkEK,uCAAgB,CAAlF;AACAC;AAEA,WAAO,MAAM;AACX,UAAIA,gCAAU;AACZL,iBAASC,iBAAiB,0BAA1B,EAAsDK;UAASC,CAAAA,SAASA,KAAKC,OAAL;QAAxE;AAEFH;;KAED,CAAA,CAZH;;AAeF,SAASD,yCAAmB;AAC1B,QAAMK,UAAUT,SAASU,cAAc,MAAvB;AAChBD,UAAQE,aAAa,0BAA0B,EAA/C;AACAF,UAAQG,WAAW;AACnBH,UAAQI,MAAMC,UAAU;AACxB,SAAOL;;;;;AE5BT,IAAMM,2CAAqB;AAC3B,IAAMC,6CAAuB;AAC7B,IAAMC,sCAAgB;EAAEC,SAAS;EAAOC,YAAY;;AAQpD,IAAMC,yCAAmB;AAgCzB,IAAMC,gDAAaC,eAAAA,YAAqD,CAACC,OAAOC,iBAAiB;AAC/F,QAAM,EAAA,OACG,OADH,UAEM,OACVC,kBAAkBC,sBAClBC,oBAAoBC,wBACpB,GAAGC,WAAH,IACEN;AACJ,QAAM,CAACO,YAAWC,YAAZ,QAA4BT,eAAAA,UAAmC,IAAnC;AAClC,QAAMG,mBAAmBO,0CAAeN,oBAAD;AACvC,QAAMC,qBAAqBK,0CAAeJ,sBAAD;AACzC,QAAMK,4BAAwBX,eAAAA,QAAiC,IAAjC;AAC9B,QAAMY,eAAeC;IAAgBX;IAAeY,CAAAA,SAASL,aAAaK,IAAD;EAArC;AAEpC,QAAMC,iBAAaf,eAAAA,QAAa;IAC9BgB,QAAQ;IACRC,QAAQ;AACN,WAAKD,SAAS;;IAEhBE,SAAS;AACP,WAAKF,SAAS;;GANC,EAQhBG;AAGHnB,qBAAAA,WAAgB,MAAM;AACpB,QAAIoB,SAAS;AACX,UAASC,gBAAT,SAAuBC,OAAmB;AACxC,YAAIP,WAAWC,UAAU,CAACR;AAAW;AACrC,cAAMe,SAASD,MAAMC;AACrB,YAAIf,WAAUgB,SAASD,MAAnB;AACFZ,gCAAsBQ,UAAUI;;AAEhCE,sCAAMd,sBAAsBQ,SAAS;YAAEO,QAAQ;WAA1C;SAIAC,iBAAT,SAAwBL,OAAmB;AACzC,YAAIP,WAAWC,UAAU,CAACR;AAAW;AACrC,cAAMoB,gBAAgBN,MAAMM;AAY5B,YAAIA,kBAAkB;AAAM;AAI5B,YAAI,CAACpB,WAAUgB,SAASI,aAAnB;AACHH,sCAAMd,sBAAsBQ,SAAS;YAAEO,QAAQ;WAA1C;SAOAG,kBAAT,SAAyBC,WAA6B;AACpD,cAAMC,iBAAiBC,SAASC;AAChC,YAAIF,mBAAmBC,SAASE;AAAM;AACtC,mBAAWC,YAAYL;AACrB,cAAIK,SAASC,aAAaC,SAAS;AAAGZ,wCAAMjB,UAAD;;AAI/CwB,eAASM,iBAAiB,WAAWjB,aAArC;AACAW,eAASM,iBAAiB,YAAYX,cAAtC;AACA,YAAMY,mBAAmB,IAAIC,iBAAiBX,eAArB;AACzB,UAAIrB;AAAW+B,yBAAiBE,QAAQjC,YAAW;UAAEkC,WAAW;UAAMC,SAAS;SAAhE;AAEf,aAAO,MAAM;AACXX,iBAASY,oBAAoB,WAAWvB,aAAxC;AACAW,iBAASY,oBAAoB,YAAYjB,cAAzC;AACAY,yBAAiBM,WAAjB;;;KAGH;IAACzB;IAASZ;IAAWO,WAAWC;GAzDnC;AA2DAhB,qBAAAA,WAAgB,MAAM;AACpB,QAAIQ,YAAW;AACbsC,6CAAiBC,IAAIhC,UAArB;AACA,YAAMiC,2BAA2BhB,SAASC;AAC1C,YAAMgB,sBAAsBzC,WAAUgB,SAASwB,wBAAnB;AAE5B,UAAI,CAACC,qBAAqB;AACxB,cAAMC,aAAa,IAAIC,YAAY1D,0CAAoBE,mCAApC;AACnBa,mBAAU8B,iBAAiB7C,0CAAoBU,gBAA/C;AACAK,mBAAU4C,cAAcF,UAAxB;AACA,YAAI,CAACA,WAAWG,kBAAkB;AAChCC,2CAAWC,kCAAYC,4CAAsBhD,UAAD,CAAtB,GAAoC;YAAEkB,QAAQ;WAA1D;AACV,cAAIM,SAASC,kBAAkBe;AAC7BvB,wCAAMjB,UAAD;;;AAKX,aAAO,MAAM;AACXA,mBAAUoC,oBAAoBnD,0CAAoBU,gBAAlD;AAKAsD,mBAAW,MAAM;AACf,gBAAMC,eAAe,IAAIP,YAAYzD,4CAAsBC,mCAAtC;AACrBa,qBAAU8B,iBAAiB5C,4CAAsBW,kBAAjD;AACAG,qBAAU4C,cAAcM,YAAxB;AACA,cAAI,CAACA,aAAaL;AAChB5B,wCAAMuB,6BAAD,QAACA,6BAAD,SAACA,2BAA4BhB,SAASE,MAAM;cAAER,QAAQ;aAAtD;AAGPlB,qBAAUoC,oBAAoBlD,4CAAsBW,kBAApD;AAEAyC,iDAAiBa,OAAO5C,UAAxB;WACC,CAXO;;;KAcb;IAACP;IAAWL;IAAkBE;IAAoBU;GAtCrD;AAyCA,QAAM6C,oBAAgB5D,eAAAA,aACnBsB,CAAAA,UAA+B;AAC9B,QAAI,CAACuC,QAAQ,CAACzC;AAAS;AACvB,QAAIL,WAAWC;AAAQ;AAEvB,UAAM8C,WAAWxC,MAAMyC,QAAQ,SAAS,CAACzC,MAAM0C,UAAU,CAAC1C,MAAM2C,WAAW,CAAC3C,MAAM4C;AAClF,UAAMnC,iBAAiBC,SAASC;AAEhC,QAAI6B,YAAY/B,gBAAgB;AAC9B,YAAMvB,YAAYc,MAAM6C;AACxB,YAAM,CAACC,OAAOC,IAAR,IAAgBC,uCAAiB9D,SAAD;AACtC,YAAM+D,4BAA4BH,SAASC;AAG3C,UAAI,CAACE,2BACH;AAAA,YAAIxC,mBAAmBvB;AAAWc,gBAAMkD,eAAN;aAC7B;AACL,YAAI,CAAClD,MAAMmD,YAAY1C,mBAAmBsC,MAAM;AAC9C/C,gBAAMkD,eAAN;AACA,cAAIX;AAAMpC,wCAAM2C,OAAO;cAAE1C,QAAQ;aAAlB;mBACNJ,MAAMmD,YAAY1C,mBAAmBqC,OAAO;AACrD9C,gBAAMkD,eAAN;AACA,cAAIX;AAAMpC,wCAAM4C,MAAM;cAAE3C,QAAQ;aAAjB;;;;KAKvB;IAACmC;IAAMzC;IAASL,WAAWC;GA3BP;AA8BtB,aACE,eAAA0D,eAAC,0CAAU,KADb,SAAA;IACiB,UAAU;KAAQnE,YAAjC;IAA6C,KAAKK;IAAc,WAAWgD;GAA3E,CAAA;CA5Je;AAgKnB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAUA,SAASN,iCAAWqB,YAA2B,EAAA,SAAW,MAATjD,IAAmB,CAAA,GAAI;AACtE,QAAMsB,2BAA2BhB,SAASC;AAC1C,aAAW2C,aAAaD,YAAY;AAClClD,gCAAMmD,WAAW;;KAAZ;AACL,QAAI5C,SAASC,kBAAkBe;AAA0B;;;AAO7D,SAASsB,uCAAiB9D,WAAwB;AAChD,QAAMmE,aAAanB,4CAAsBhD,SAAD;AACxC,QAAM4D,QAAQS,kCAAYF,YAAYnE,SAAb;AACzB,QAAM6D,OAAOQ,kCAAYF,WAAWG,QAAX,GAAsBtE,SAAvB;AACxB,SAAO;IAAC4D;IAAOC;;;AAajB,SAASb,4CAAsBhD,WAAwB;AACrD,QAAMuE,QAAuB,CAAA;AAC7B,QAAMC,SAAShD,SAASiD,iBAAiBzE,WAAW0E,WAAWC,cAAc;IAC3EC,YAAatE,CAAAA,SAAc;AACzB,YAAMuE,gBAAgBvE,KAAKwE,YAAY,WAAWxE,KAAKyE,SAAS;AAChE,UAAIzE,KAAK0E,YAAY1E,KAAK2E,UAAUJ;AAAe,eAAOH,WAAWQ;AAIrE,aAAO5E,KAAK6E,YAAY,IAAIT,WAAWU,gBAAgBV,WAAWQ;;GAPvD;AAUf,SAAOV,OAAOa,SAAP;AAAmBd,UAAMe,KAAKd,OAAOe,WAAlB;AAG1B,SAAOhB;;AAOT,SAASF,kCAAYmB,UAAyBxF,WAAwB;AACpE,aAAWyF,WAAWD,UAAU;AAE9B,QAAI,CAACE,+BAASD,SAAS;MAAEE,MAAM3F;KAAlB;AAAgC,aAAOyF;;;AAIxD,SAASC,+BAASpF,MAAmB,EAAA,KAAEqF,GAAgC;AACrE,MAAIC,iBAAiBtF,IAAD,EAAOuF,eAAe;AAAU,WAAO;AAC3D,SAAOvF,MAAM;AAEX,QAAIqF,SAASG,UAAaxF,SAASqF;AAAM,aAAO;AAChD,QAAIC,iBAAiBtF,IAAD,EAAOyF,YAAY;AAAQ,aAAO;AACtDzF,WAAOA,KAAK0F;;AAEd,SAAO;;AAGT,SAASC,wCAAkBR,SAAmE;AAC5F,SAAOA,mBAAmBS,oBAAoB,YAAYT;;AAG5D,SAASxE,4BAAMwE,SAAkC,EAAA,SAAW,MAATvE,IAAmB,CAAA,GAAI;AAExE,MAAIuE,WAAWA,QAAQxE,OAAO;AAC5B,UAAMuB,2BAA2BhB,SAASC;AAE1CgE,YAAQxE,MAAM;MAAEkF,eAAe;KAA/B;AAEA,QAAIV,YAAYjD,4BAA4ByD,wCAAkBR,OAAD,KAAavE;AACxEuE,cAAQvE,OAAR;;;AASN,IAAMoB,yCAAmB8D,6CAAsB;AAE/C,SAASA,+CAAyB;AAEhC,MAAIC,QAAyB,CAAA;AAE7B,SAAO;IACL9D,IAAIhC,YAA2B;AAE7B,YAAM+F,mBAAmBD,MAAM,CAAD;AAC9B,UAAI9F,eAAe+F;AACjBA,6BAAgB,QAAhBA,qBAAgB,UAAhBA,iBAAkB7F,MAAlB;AAGF4F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB8F,YAAMG,QAAQjG,UAAd;;IAGF4C,OAAO5C,YAA2B;AAAA,UAAA;AAChC8F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB,OAAA,UAAA8F,MAAM,CAAD,OAAL,QAAA,YAAA,UAAA,QAAU3F,OAAV;;;;AAKN,SAAS6F,kCAAeE,OAAYC,MAAS;AAC3C,QAAMC,eAAe;OAAIF;;AACzB,QAAMG,QAAQD,aAAaE,QAAQH,IAArB;AACd,MAAIE,UAAU;AACZD,iBAAaG,OAAOF,OAAO,CAA3B;AAEF,SAAOD;;AAGT,SAAS5D,kCAAYgE,OAAsB;AACzC,SAAOA,MAAMC;IAAQN,CAAAA,SAASA,KAAK5B,YAAY;EAAxC;;;;;;;;AE9UT,IAAMmC,4CAAkBC,QAAQC,eAAD,QAACA,eAAD,SAAA,SAACA,WAAYC,QAAb,IAAyBC,eAAAA,kBAAwB,MAAM;AAAA;;;AELtF,IAAMC,mCAAcC,aAAc,QAAQC,SAAR,CAAf,MAAuC,MAAMC;AAChE,IAAIC,8BAAQ;AAEZ,SAASC,0CAAMC,iBAAkC;AAC/C,QAAM,CAACC,IAAIC,KAAL,IAAoBC,sBAA6BT,iCAAU,CAA7C;AAEpBU,4CAAgB,MAAM;AACpB,QAAI,CAACJ;AAAiBE;QAAOG,CAAAA,YAAYA,YAAb,QAAaA,YAAb,SAAaA,UAAWC,OAAOR,6BAAD;MAA/B;KAC1B;IAACE;GAFW;AAGf,SAAOA,oBAAoBC,KAAM,SAAQA,EAAG,KAAI;;;;;;;;AEJlD,IAAMM,6BAAO;AAMb,IAAMC,gDAAQC,eAAAA,YAA2C,CAACC,OAAOC,iBAAiB;AAChF,QAAM,EAAA,UAAA,QAAoB,IAApB,SAAiC,GAAG,GAAGC,WAAH,IAAkBF;AAC5D,aACE,eAAAG,eAAC,0CAAU,KAAX,SAAA,CAAA,GACMD,YAFR;IAGI,KAAKD;IACL;IACA;IACA,SAAQ;IACR,qBAAoB;GANtB,GASGD,MAAMI,UAAUC,eAAW,eAAAF,eAT9B,WAAA;IASuC,QAAO;GAAhB,CAT9B;CAHU;AAiBd,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,IAAMG,4CAAOR;;;;AE/Bb,SAASS,0CAAQC,SAA6B;AAC5C,QAAM,CAACC,OAAMC,OAAP,QAAkBC,eAAAA,UAA8DC,MAA9D;AAExBC,4CAAgB,MAAM;AACpB,QAAIL,SAAS;AAEXE,cAAQ;QAAEI,OAAON,QAAQO;QAAaC,QAAQR,QAAQS;OAA/C;AAEP,YAAMC,iBAAiB,IAAIC,eAAgBC,CAAAA,YAAY;AACrD,YAAI,CAACC,MAAMC,QAAQF,OAAd;AACH;AAKF,YAAI,CAACA,QAAQG;AACX;AAGF,cAAMC,QAAQJ,QAAQ,CAAD;AACrB,YAAIN;AACJ,YAAIE;AAEJ,YAAI,mBAAmBQ,OAAO;AAC5B,gBAAMC,kBAAkBD,MAAM,eAAD;AAE7B,gBAAME,aAAaL,MAAMC,QAAQG,eAAd,IAAiCA,gBAAgB,CAAD,IAAMA;AACzEX,kBAAQY,WAAW,YAAD;AAClBV,mBAASU,WAAW,WAAD;eACd;AAGLZ,kBAAQN,QAAQO;AAChBC,mBAASR,QAAQS;;AAGnBP,gBAAQ;UAzChB;UAAA;SAyCe;OA5Bc;AA+BvBQ,qBAAeS,QAAQnB,SAAS;QAAEoB,KAAK;OAAvC;AAEA,aAAO,MAAMV,eAAeW,UAAUrB,OAAzB;;AAIbE,cAAQE,MAAD;KAER;IAACJ;GA5CW;AA8Cf,SAAOC;;;;AEnBT,IAAMqB,oCAAc;AAGpB,IAAM,CAACC,2CAAqBC,uCAAtB,IAA2CC,yCAAmBH,iCAAD;AAMnE,IAAM,CAACI,sCAAgBC,sCAAjB,IAAqCJ,0CAAwCD,iCAArB;AAK9D,IAAMM,4CAAiCC,CAAAA,UAAoC;AACzE,QAAM,EAAA,eAAA,SAAiBC,IAAaD;AACpC,QAAM,CAACE,QAAQC,SAAT,QAAsBC,eAAAA,UAAkC,IAAlC;AAC5B,aACE,eAAAC,eAAC,sCADH;IACkB,OAAOC;IAAe;IAAgB,gBAAgBH;KACnEF,QADH;;AAMJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMM,oCAAc;AAQpB,IAAMC,+CAAeJ,eAAAA,YACnB,CAACJ,OAAuCS,iBAAiB;AACvD,QAAM,EAAA,eAAA,YAA6B,GAAGC,YAAH,IAAmBV;AACtD,QAAMW,UAAUb,uCAAiBS,mCAAaD,aAAd;AAChC,QAAMM,UAAMR,eAAAA,QAAkC,IAAlC;AACZ,QAAMS,eAAeC,0CAAgBL,cAAcG,GAAf;AAEpCR,qBAAAA,WAAgB,MAAM;AAIpBO,YAAQI,gBAAeC,eAAU,QAAVA,eAAU,SAAV,SAAAA,WAAYC,YAAWL,IAAIK,OAAlD;GAJF;AAOA,SAAOD,aAAa,WAAO,eAAAX,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBK,aAA9C;IAA2D,KAAKG;GAArC,CAAA;CAdV;AAkBrB,OAAA,OAAA,0CAAA;EAAA,aAAA;CAAA;AAMA,IAAMK,qCAAe;AAUrB,IAAM,CAACC,6CAAuBC,uCAAxB,IACJ1B,0CAA+CwB,kCAA5B;AAoBrB,IAAMG,+CAAgBjB,eAAAA,YACpB,CAACJ,OAAwCS,iBAAiB;AAAA,MAAA,kBAAA,mBAAA,uBAAA,wBAAA,wBAAA,uBAAA,wBAAA;AACxD,QAAM,EAAA,eAAA,OAEG,UAFH,aAGS,GAHT,QAII,UAJJ,cAKU,GALV,eAMW,GANX,kBAOc,MAPd,oBAQgB,CAAA,GACpBa,kBAAkBC,uBAAuB,GATrC,SAUK,WAVL,mBAWe,OAXf,yBAYqB,aAZrB,UAcJ,GAAGC,aAAH,IACExB;AAEJ,QAAMW,UAAUb,uCAAiBoB,oCAAcZ,aAAf;AAEhC,QAAM,CAACmB,SAASC,UAAV,QAAwBtB,eAAAA,UAAsC,IAAtC;AAC9B,QAAMS,eAAeC;IAAgBL;IAAekB,CAAAA,SAASD,WAAWC,IAAD;EAAnC;AAEpC,QAAM,CAACC,QAAOC,QAAR,QAAoBzB,eAAAA,UAAuC,IAAvC;AAC1B,QAAM0B,YAAYC,0CAAQH,MAAD;AACzB,QAAMI,cAAU,mBAAGF,cAAH,QAAGA,cAAH,SAAA,SAAGA,UAAWG,WAAd,QAAA,qBAAA,SAAA,mBAAuB;AACvC,QAAMC,eAAW,oBAAGJ,cAAH,QAAGA,cAAH,SAAA,SAAGA,UAAWK,YAAd,QAAA,sBAAA,SAAA,oBAAwB;AAEzC,QAAMC,mBAAoBC,QAAQC,UAAU,WAAW,MAAMA,QAAQ;AAErE,QAAMhB,mBACJ,OAAOC,yBAAyB,WAC5BA,uBACA;IAAEgB,KAAK;IAAGC,OAAO;IAAGC,QAAQ;IAAGC,MAAM;IAAG,GAAGnB;;AAEjD,QAAMoB,WAAWC,MAAMC,QAAQC,iBAAd,IAAmCA,oBAAoB;IAACA;;AACzE,QAAMC,wBAAwBJ,SAASK,SAAS;AAEhD,QAAMC,wBAAwB;IAC5BC,SAAS5B;IACTqB,UAAUA,SAASQ,OAAOC,+BAAhB;;IAEVC,aAAaN;;AAGf,QAAM,EAAA,MAAA,gBAAA,WAAA,cAAA,eAAiDO,IAAmBC,YAAY;;IAEpFC,UAAU;IACVC,WAAWrB;IACXsB,sBAAsB,IAAIC,SAAS;AACjC,YAAMC,UAAUC,WAAU,GAAIF,MAAM;QAClCG,gBAAgBC,2BAA2B;OADnB;AAG1B,aAAOH;;IAETI,UAAU;MACRC,WAAWtD,QAAQT;;IAErBgE,YAAY;MACVC,OAAO;QAAEC,UAAUC,aAAanC;QAAaoC,eAAeC;OAAtD;MACNC,mBACEC,MAAM;QACJL,UAAU;QACVM,WAAW;QACXC,SAASC,WAAW,YAAYC,WAAU,IAAKC;QAC/C,GAAG7B;OAJA;MAMPuB,mBAAmBO,KAAK;QAAE,GAAG9B;OAAN;MACvB+B,KAAK;QACH,GAAG/B;QACHgC,OAAO,CAAC,EAAA,UAAA,OAAA,gBAAA,gBAAmCC,MAAsB;AAC/D,gBAAM,EAAEjD,OAAOkD,aAAahD,QAAQiD,aAARjD,IAAyBkD,MAAMpB;AAC3D,gBAAMqB,eAAetB,SAASuB,SAASC;AACvCF,uBAAaG,YAAY,kCAAmC,GAAEC,cAAe,IAA7E;AACAJ,uBAAaG,YAAY,mCAAoC,GAAEP,eAAgB,IAA/E;AACAI,uBAAaG,YAAY,+BAAgC,GAAEN,WAAY,IAAvE;AACAG,uBAAaG,YAAY,gCAAiC,GAAEL,YAAa,IAAzE;;OARA;MAWJxD,UAAS+D,MAAgB;QAAEC,SAAShE;QAAOsB,SAAS2C;OAA5B;MACxBC,sCAAgB;;;OAAD;MACfC,oBAAoBC,KAAK;QAAExC,UAAU;QAAmB,GAAGP;OAAnC;;GApCyD;AAwCrF,QAAM,CAACgD,YAAYC,WAAb,IAA4BC,mDAA6B1C,SAAD;AAE9D,QAAM2C,eAAeC,0CAAeC,QAAD;AACnCC,4CAAgB,MAAM;AACpB,QAAIC;AACFJ,uBAAY,QAAZA,iBAAY,UAAZA,aAAY;KAEb;IAACI;IAAcJ;GAJH;AAMf,QAAMK,UAAM,wBAAGnD,eAAe1B,WAAlB,QAAA,0BAAA,SAAA,SAAG,sBAAsB8E;AACrC,QAAMC,UAAM,yBAAGrD,eAAe1B,WAAlB,QAAA,2BAAA,SAAA,SAAG,uBAAsBgF;AACrC,QAAMC,sBAAoB,yBAAAvD,eAAe1B,WAAf,QAAA,2BAAA,SAAA,SAAA,uBAAsBkF,kBAAiB;AAEjE,QAAM,CAACC,eAAeC,gBAAhB,QAAoC5G,eAAAA,UAAA;AAC1CmG,4CAAgB,MAAM;AACpB,QAAI9E;AAASuF,uBAAiBC,OAAOC,iBAAiBzF,OAAxB,EAAiC0F,MAAlC;KAC5B;IAAC1F;GAFW;AAIf,aACE,eAAApB,eADF,OAAA;IAEI,KAAK+G,KAAKC;IACV,qCAAkC;IAClC,OAAO;MACL,GAAGC;MACHC,WAAWf,eAAec,eAAeC,YAAY;;MACrDC,UAAU;MACVL,QAAQJ;MACR,CAAC,iCAAD,GAA4C;SAAA,wBAC1CzD,eAAewC,qBAD2B,QAAA,0BAAA,SAAA,SAC1C,sBAAgCY;SADU,yBAE1CpD,eAAewC,qBAF2B,QAAA,2BAAA,SAAA,SAE1C,uBAAgCc;QAChCa,KAAK,GAHqC;;IAQ9C,KAAKzH,MAAM0H;SAEX,eAAArH,eAAC,6CAlBH;IAmBI,OAAOC;IACP;IACA,eAAeuB;IACf;IACA;IACA,iBAAiBgF;SAEjB,eAAAxG,eAAC,0CAAU,KARb,SAAA;IASI,aAAW4F;IACX,cAAYC;KACR1E,cAHN;IAIE,KAAKX;IACL,OAAO;MACL,GAAGW,aAAagE;;;MAGhBmC,WAAW,CAACnB,eAAe,SAAS1B;;MAEpC8C,UAAS,uBAAAtE,eAAe0C,UAAf,QAAA,yBAAA,UAAA,qBAAqB6B,kBAAkB,IAAI/C;;GAXxD,CAAA,CARF,CAlBF;CAzGgB;AAuJtB,OAAA,OAAA,0CAAA;EAAA,aAAA;CAAA;AAMA,IAAMgD,mCAAa;AAEnB,IAAMC,sCAAoC;EACxCxF,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,MAAM;;AAOR,IAAMsF,gDAAc5H,eAAAA,YAAuD,SAAS4H,2CAClFhI,OACAS,cACA;AACA,QAAM,EAAA,eAAiB,GAAGwH,WAAH,IAAkBjI;AACzC,QAAMkI,iBAAiB9G,wCAAkB0G,kCAAYxH,aAAb;AACxC,QAAM6H,WAAWJ,oCAAcG,eAAejC,UAAhB;AAE9B;;;;QAIE,eAAA5F,eAAA,QAAA;MACE,KAAK6H,eAAeE;MACpB,OAAO;QACLC,UAAU;QACV3F,MAAMwF,eAAezB;QACrBlE,KAAK2F,eAAevB;QACpB,CAACwB,QAAD,GAAY;QACZrC,iBAAiB;UACfvD,KAAK;UACLC,OAAO;UACPC,QAAQ;UACRC,MAAM;UACNwF,eAAejC,UALA;QAMjBsB,WAAW;UACThF,KAAK;UACLC,OAAO;UACPC,QAAS;UACTC,MAAM;UACNwF,eAAejC,UALN;QAMXqC,YAAYJ,eAAeK,kBAAkB,WAAWzD;;WAG1D,eAAAzE,eAAC,2CAAD,SAAA,CAAA,GACM4H,YAvBR;MAwBI,KAAKxH;MACL,OAAO;QACL,GAAGwH,WAAWzC;;QAEdgD,SAAS;;KANb,CAAA,CAtBF;;CAZgB;AA+CpB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,SAASpF,gCAAaqF,OAA6B;AACjD,SAAOA,UAAU;;AAGnB,IAAM3C,wCAAmB4C,CAAAA,aAAsE;EAC7FC,MAAM;;EAENC,GAAGC,MAAM;AAAA,QAAA,wBAAA,wBAAA,wBAAA,wBAAA;AACP,UAAM,EAAA,WAAA,OAAA,eAAoBvF,IAAmBuF;AAE7C,UAAMhC,sBAAoB,yBAAAvD,eAAe1B,WAAf,QAAA,2BAAA,SAAA,SAAA,uBAAsBkF,kBAAiB;AACjE,UAAMgC,gBAAgBjC;AACtB,UAAM7E,aAAa8G,gBAAgB,IAAIJ,QAAQ1G;AAC/C,UAAME,cAAc4G,gBAAgB,IAAIJ,QAAQxG;AAEhD,UAAM,CAAC+D,YAAYC,WAAb,IAA4BC,mDAA6B1C,SAAD;AAC9D,UAAMsF,eAAe;MAAEC,OAAO;MAAMC,QAAQ;MAAOC,KAAK;MAAShD,WAA5C;AAErB,UAAMiD,iBAAe,0BAAA,yBAAC7F,eAAe1B,WAAhB,QAAA,2BAAA,SAAA,SAAC,uBAAsB8E,OAAvB,QAAA,2BAAA,SAAA,yBAA4B,KAAK1E,aAAa;AACnE,UAAMoH,iBAAe,0BAAA,yBAAC9F,eAAe1B,WAAhB,QAAA,2BAAA,SAAA,SAAC,uBAAsBgF,OAAvB,QAAA,2BAAA,SAAA,yBAA4B,KAAK1E,cAAc;AAEpE,QAAIwE,IAAI;AACR,QAAIE,IAAI;AAER,QAAIX,eAAe,UAAU;AAC3BS,UAAIoC,gBAAgBC,eAAgB,GAAEI,YAAa;AACnDvC,UAAK,GAAE,CAAC1E,WAAY;eACX+D,eAAe,OAAO;AAC/BS,UAAIoC,gBAAgBC,eAAgB,GAAEI,YAAa;AACnDvC,UAAK,GAAEvB,MAAME,SAASpD,SAASD,WAAY;eAClC+D,eAAe,SAAS;AACjCS,UAAK,GAAE,CAACxE,WAAY;AACpB0E,UAAIkC,gBAAgBC,eAAgB,GAAEK,YAAa;eAC1CnD,eAAe,QAAQ;AAChCS,UAAK,GAAErB,MAAME,SAAStD,QAAQC,WAAY;AAC1C0E,UAAIkC,gBAAgBC,eAAgB,GAAEK,YAAa;;AAErD,WAAO;MAAEP,MAAM;;;;;;;AAInB,SAAS1C,mDAA6B1C,WAAsB;AAC1D,QAAM,CAACpB,MAAMC,QAAQ,QAAf,IAA2BmB,UAAU4F,MAAM,GAAhB;AACjC,SAAO;IAAChH;IAAcC;;;AAGxB,IAAMgH,4CAAOvJ;AACb,IAAMwJ,4CAAS/I;AACf,IAAMgJ,4CAAUnI;AAChB,IAAMoI,4CAAQzB;;;;;AEpYd,IAAM0B,oCAAc;AAWpB,IAAMC,gDAASC,eAAAA,YAA6C,CAACC,OAAOC,iBAAiB;AAAA,MAAA;AACnF,QAAM,EAAA,YAAcC,eAAH,QAAGA,eAAH,SAAA,UAAA,uBAAGA,WAAYC,cAAf,QAAA,yBAAA,SAAA,SAAG,qBAAsBC,MAAM,GAAGC,YAAH,IAAmBL;AACnE,SAAOM,YACHC,kBAAAA,QAASC,iBAAa,eAAAC,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBJ,aAD7C;IAC0D,KAAKJ;GAArC,CAAA,GAAuDK,SAA7E,IACA;CAJS;AAOf,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;;;;;AGjBO,SAASI,0CACdC,cACAC,SACA;AACA,aAAOC,eAAAA,YAAiB,CAACC,OAAwBC,UAA4C;AAC3F,UAAMC,YAAaJ,QAAQE,KAAD,EAAgBC,KAAxB;AAClB,WAAOC,cAAP,QAAOA,cAAP,SAAOA,YAAaF;KACnBH,YAHI;;ADJT,IAAMM,4CAAqCC,CAAAA,UAAU;AACnD,QAAM,EAZR,SAAA,SAYmBC,IAAaD;AAC9B,QAAME,WAAWC,kCAAYC,OAAD;AAE5B,QAAMC,QACJ,OAAOJ,aAAa,aAChBA,SAAS;IAAEG,SAASF,SAASI;GAArB,IACRX,eAAAA,SAAeY,KAAKN,QAApB;AAGN,QAAMO,MAAMC,0CAAgBP,SAASM,KAAMH,MAAcG,GAA9B;AAC3B,QAAME,aAAa,OAAOT,aAAa;AACvC,SAAOS,cAAcR,SAASI,gBAAYX,eAAAA,cAAmBU,OAAO;IAvBtE;GAuB4C,IAAqC;;AAGjFN,0CAASY,cAAc;AAMvB,SAASR,kCAAYC,SAAkB;AACrC,QAAM,CAACQ,OAAMC,OAAP,QAAkBlB,eAAAA,UAAA;AACxB,QAAMmB,gBAAYnB,eAAAA,QAAkC,CAAA,CAAlC;AAClB,QAAMoB,qBAAiBpB,eAAAA,QAAaS,OAAb;AACvB,QAAMY,2BAAuBrB,eAAAA,QAAqB,MAArB;AAC7B,QAAMF,eAAeW,UAAU,YAAY;AAC3C,QAAM,CAACR,OAAOqB,IAAR,IAAgBzB,0CAAgBC,cAAc;IAClDyB,SAAS;MACPC,SAAS;MACTC,eAAe;;IAEjBC,kBAAkB;MAChBC,OAAO;MACPC,eAAe;;IAEjBC,WAAW;MACTF,OAAO;;GAV0B;AAcrC3B,qBAAAA,WAAgB,MAAM;AACpB,UAAM8B,uBAAuBC,uCAAiBZ,UAAUa,OAAX;AAC7CX,yBAAqBW,UAAU/B,UAAU,YAAY6B,uBAAuB;KAC3E;IAAC7B;GAHJ;AAKAgC,4CAAgB,MAAM;AACpB,UAAMC,SAASf,UAAUa;AACzB,UAAMG,aAAaf,eAAeY;AAClC,UAAMI,oBAAoBD,eAAe1B;AAEzC,QAAI2B,mBAAmB;AACrB,YAAMC,oBAAoBhB,qBAAqBW;AAC/C,YAAMF,uBAAuBC,uCAAiBG,MAAD;AAE7C,UAAIzB;AACFa,aAAK,OAAD;eACKQ,yBAAyB,WAAUI,WAAM,QAANA,WAAM,SAAN,SAAAA,OAAQI,aAAY;AAGhEhB,aAAK,SAAD;WACC;AAOL,cAAMiB,cAAcF,sBAAsBP;AAE1C,YAAIK,cAAcI;AAChBjB,eAAK,eAAD;;AAEJA,eAAK,SAAD;;AAIRF,qBAAeY,UAAUvB;;KAE1B;IAACA;IAASa;GAjCE;AAmCfW,4CAAgB,MAAM;AACpB,QAAIhB,OAAM;AAMR,YAAMuB,qBAAsBtC,CAAAA,UAA0B;AACpD,cAAM4B,uBAAuBC,uCAAiBZ,UAAUa,OAAX;AAC7C,cAAMS,qBAAqBX,qBAAqBY,SAASxC,MAAMyC,aAApC;AAC3B,YAAIzC,MAAM0C,WAAW3B,SAAQwB;AAI3BI,gCAAAA;YAAmB,MAAMvB,KAAK,eAAD;UAA7B;;AAGJ,YAAMwB,uBAAwB5C,CAAAA,UAA0B;AACtD,YAAIA,MAAM0C,WAAW3B;AAEnBI,+BAAqBW,UAAUD,uCAAiBZ,UAAUa,OAAX;;AAGnDf,YAAK8B,iBAAiB,kBAAkBD,oBAAxC;AACA7B,YAAK8B,iBAAiB,mBAAmBP,kBAAzC;AACAvB,YAAK8B,iBAAiB,gBAAgBP,kBAAtC;AACA,aAAO,MAAM;AACXvB,cAAK+B,oBAAoB,kBAAkBF,oBAA3C;AACA7B,cAAK+B,oBAAoB,mBAAmBR,kBAA5C;AACAvB,cAAK+B,oBAAoB,gBAAgBR,kBAAzC;;;AAKFlB,WAAK,eAAD;KAEL;IAACL;IAAMK;GApCK;AAsCf,SAAO;IACLX,WAAW;MAAC;MAAW;MAAoB+B,SAASzC,KAAzC;IACXY,SAAKb,eAAAA,aAAmBiB,CAAAA,SAAsB;AAC5C,UAAIA;AAAME,kBAAUa,UAAUiB,iBAAiBhC,IAAD;AAC9CC,cAAQD,IAAD;OACN,CAAA,CAHE;;;AAST,SAASc,uCAAiBG,QAA8B;AACtD,UAAOA,WAAM,QAANA,WAAM,SAAN,SAAAA,OAAQS,kBAAiB;;;;;AGhIlC,IAAMO,oCAAc;AACpB,IAAMC,sCAAgB;EAAEC,SAAS;EAAOC,YAAY;;AAMpD,IAAMC,mCAAa;AAGnB,IAAM,CAACC,kCAAYC,qCAAeC,2CAA5B,IAAqDC,0CAGzDJ,gCAHyE;AAM3E,IAAM,CAACK,qDAA+BC,wCAAhC,IAA+DC,yCACnEP,kCACA;EAACG;CAFoF;AAiCvF,IAAM,CAACK,2CAAqBC,2CAAtB,IACJJ,oDAAkDL,gCAArB;AAK/B,IAAMU,gDAAmBC,eAAAA,YACvB,CAACC,OAA2CC,iBAAiB;AAC3D,aACE,eAAAC,eAAC,iCAAW,UADd;IACuB,OAAOF,MAAMG;SAChC,eAAAD,eAAC,iCAAW,MADd;IACmB,OAAOF,MAAMG;SAC5B,eAAAD,eAAC,4CAAD,SAAA,CAAA,GAA0BF,OAD5B;IACmC,KAAKC;GAAtC,CAAA,CADF,CADF;CAHmB;AAYzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAeA,IAAMG,iDAAuBL,eAAAA,YAG3B,CAACC,OAA+CC,iBAAiB;AACjE,QAAM,EAAA,yBAAA,aAAA,OAGG,OAHH,KAKJI,kBAAkBC,sBALd,yBAAA,0BAAA,cASJ,GAAGC,WAAH,IACEP;AACJ,QAAMQ,UAAMT,eAAAA,QAA0C,IAA1C;AACZ,QAAMU,eAAeC,0CAAgBT,cAAcO,GAAf;AACpC,QAAMG,YAAYC,0CAAaC,GAAD;AAC9B,QAAM,CAACR,mBAAmB,MAAMS,mBAA1B,IAAiDC,yCAAqB;IAC1EC,MAAMV;IACNW,aAAaC;IACbC,UAAUC;GAH+D;AAK3E,QAAM,CAACC,kBAAkBC,mBAAnB,QAA0CvB,eAAAA,UAAe,KAAf;AAChD,QAAMwB,mBAAmBC,0CAAeC,YAAD;AACvC,QAAMC,WAAWpC,oCAAca,uBAAD;AAC9B,QAAMwB,sBAAkB5B,eAAAA,QAAa,KAAb;AACxB,QAAM,CAAC6B,qBAAqBC,sBAAtB,QAAgD9B,eAAAA,UAAe,CAAf;AAEtDA,qBAAAA,WAAgB,MAAM;AACpB,UAAM+B,OAAOtB,IAAIuB;AACjB,QAAID,MAAM;AACRA,WAAKE,iBAAiBhD,mCAAauC,gBAAnC;AACA,aAAO,MAAMO,KAAKG,oBAAoBjD,mCAAauC,gBAAtC;;KAEd;IAACA;GANJ;AAQA,aACE,eAAArB,eAAC,2CADH;IAEI,OAAOC;IACP;IACA,KAAKQ;IACL;IACA;IACA,iBAAaZ,eAAAA;MACVmC,CAAAA,cAAcpB,oBAAoBoB,SAAD;MAClC;QAACpB;;IAFU;IAIb,oBAAgBf,eAAAA;MAAkB,MAAMuB,oBAAoB,IAAD;MAAQ,CAAA;IAAnD;IAChB,wBAAoBvB,eAAAA;MAClB,MAAM8B;QAAwBM,CAAAA,cAAcA,YAAY;MAA5B;MAC5B,CAAA;IAFkB;IAIpB,2BAAuBpC,eAAAA;MACrB,MAAM8B;QAAwBM,CAAAA,cAAcA,YAAY;MAA5B;MAC5B,CAAA;IAFqB;SAKvB,eAAAjC,eAAC,0CAAU,KApBb,SAAA;IAqBI,UAAUmB,oBAAoBO,wBAAwB,IAAI,KAAK;IAC/D,oBAAkBQ;KACd7B,YAHN;IAIE,KAAKE;IACL,OAAO;MAAE4B,SAAS;MAAQ,GAAGrC,MAAMsC;;IACnC,aAAaC,0CAAqBvC,MAAMwC,aAAa,MAAM;AACzDb,sBAAgBI,UAAU;KADK;IAGjC,SAASQ,0CAAqBvC,MAAMyC,SAAUC,CAAAA,UAAU;AAKtD,YAAMC,kBAAkB,CAAChB,gBAAgBI;AAEzC,UAAIW,MAAME,WAAWF,MAAMG,iBAAiBF,mBAAmB,CAACtB,kBAAkB;AAChF,cAAMyB,kBAAkB,IAAIC,YAAY/D,mCAAaC,mCAA7B;AACxByD,cAAMG,cAAcG,cAAcF,eAAlC;AAEA,YAAI,CAACA,gBAAgBG,kBAAkB;AACrC,gBAAMC,QAAQxB,SAAQ,EAAGyB;YAAQC,CAAAA,SAASA,KAAKC;UAAjC;AACd,gBAAMC,aAAaJ,MAAMK;YAAMH,CAAAA,SAASA,KAAKI;UAA1B;AACnB,gBAAMC,cAAcP,MAAMK;YAAMH,CAAAA,SAASA,KAAKM,OAAOrD;UAAjC;AACpB,gBAAMsD,iBAAiB;YAACL;YAAYG;eAAgBP;YAAOC,OACzDS,OADqB;AAGvB,gBAAMC,iBAAiBF,eAAeG;YAAKV,CAAAA,SAASA,KAAK5C,IAAIuB;UAAtC;AACvBgC,2CAAWF,cAAD;;;AAIdlC,sBAAgBI,UAAU;KAvBC;IAyB7B,QAAQQ;MAAqBvC,MAAMgE;MAAQ,MAAM1C,oBAAoB,KAAD;IAAxC;GAlC9B,CAAA,CApBF;CAtCyB;AAsG7B,IAAM2C,kCAAY;AAUlB,IAAMC,+CAAuBnE,eAAAA,YAC3B,CAACC,OAA0CC,iBAAiB;AAC1D,QAAM,EAAA,yBAAA,YAEQ,MAFR,SAGK,OAHL,WAKJ,GAAGkE,UAAH,IACEnE;AACJ,QAAMoE,SAASC,0CAAK;AACpB,QAAMX,KAAKxB,aAAakC;AACxB,QAAME,UAAUzE,4CAAsBoE,iCAAW9D,uBAAZ;AACrC,QAAMoE,mBAAmBD,QAAQjE,qBAAqBqD;AACtD,QAAMhC,WAAWpC,oCAAca,uBAAD;AAE9B,QAAM,EAAA,oBAAA,sBAAsBqE,IAA0BF;AAEtDvE,qBAAAA,WAAgB,MAAM;AACpB,QAAIsD,WAAW;AACboB,yBAAkB;AAClB,aAAO,MAAMD,sBAAqB;;KAEnC;IAACnB;IAAWoB;IAAoBD;GALnC;AAOA,aACE,eAAAtE,eAAC,iCAAW,UADd;IAEI,OAAOC;IACP;IACA;IACA;SAEA,eAAAD,eAAC,0CAAU,MANb,SAAA;IAOI,UAAUqE,mBAAmB,IAAI;IACjC,oBAAkBD,QAAQlC;KACtB+B,WAHN;IAIE,KAAKlE;IACL,aAAasC,0CAAqBvC,MAAMwC,aAAcE,CAAAA,UAAU;AAG9D,UAAI,CAACW;AAAWX,cAAMgC,eAAN;;AAEXJ,gBAAQK,YAAYjB,EAApB;KAL0B;IAOjC,SAASnB;MAAqBvC,MAAMyC;MAAS,MAAM6B,QAAQK,YAAYjB,EAApB;IAAtB;IAC7B,WAAWnB,0CAAqBvC,MAAM4E,WAAYlC,CAAAA,UAAU;AAC1D,UAAIA,MAAMmC,QAAQ,SAASnC,MAAMoC,UAAU;AACzCR,gBAAQS,eAAR;AACA;;AAGF,UAAIrC,MAAME,WAAWF,MAAMG;AAAe;AAE1C,YAAMmC,cAAcC,qCAAevC,OAAO4B,QAAQlC,aAAakC,QAAQzD,GAArC;AAElC,UAAImE,gBAAgBE,QAAW;AAC7BxC,cAAMgC,eAAN;AACA,cAAMxB,QAAQxB,SAAQ,EAAGyB;UAAQC,CAAAA,SAASA,KAAKC;QAAjC;AACd,YAAIQ,iBAAiBX,MAAMY;UAAKV,CAAAA,SAASA,KAAK5C,IAAIuB;QAA7B;AAErB,YAAIiD,gBAAgB;AAAQnB,yBAAesB,QAAf;iBACnBH,gBAAgB,UAAUA,gBAAgB,QAAQ;AACzD,cAAIA,gBAAgB;AAAQnB,2BAAesB,QAAf;AAC5B,gBAAMC,eAAevB,eAAewB,QAAQ3C,MAAMG,aAA7B;AACrBgB,2BAAiBS,QAAQgB,OACrBC,gCAAU1B,gBAAgBuB,eAAe,CAAhC,IACTvB,eAAe2B,MAAMJ,eAAe,CAApC;;AAONK;UAAW,MAAM1B,iCAAWF,cAAD;QAAjB;;KA5BiB;GAbjC,CAAA,CANF;CAzBuB;AAiF7B,OAAA,OAAA,0CAAA;EAAA,aAAA;CAAA;AAKA,IAAM6B,gDAAuD;EAC3DC,WAAW;EAAQC,SAAS;EAC5BC,YAAY;EAAQC,WAAW;EAC/BC,QAAQ;EAASC,MAAM;EACvBC,UAAU;EAAQC,KAAK;;AAGzB,SAASC,2CAAqBtB,KAAahE,KAAiB;AAC1D,MAAIA,QAAQ;AAAO,WAAOgE;AAC1B,SAAOA,QAAQ,cAAc,eAAeA,QAAQ,eAAe,cAAcA;;AAKnF,SAASI,qCAAevC,OAA4BN,aAA2BvB,KAAiB;AAC9F,QAAMgE,MAAMsB,2CAAqBzD,MAAMmC,KAAKhE,GAAZ;AAChC,MAAIuB,gBAAgB,cAAc;IAAC;IAAa;IAAcgE,SAASvB,GAArC;AAA2C,WAAOK;AACpF,MAAI9C,gBAAgB,gBAAgB;IAAC;IAAW;IAAagE,SAASvB,GAAlC;AAAwC,WAAOK;AACnF,SAAOQ,8CAAwBb,GAAD;;AAGhC,SAASd,iCAAWsC,YAA2B;AAC7C,QAAMC,6BAA6BC,SAASC;AAC5C,aAAWC,aAAaJ,YAAY;AAElC,QAAII,cAAcH;AAA4B;AAC9CG,cAAUC,MAAV;AACA,QAAIH,SAASC,kBAAkBF;AAA4B;;;AAQ/D,SAASf,gCAAaoB,OAAYC,YAAoB;AACpD,SAAOD,MAAM7C;IAAI,CAAC+C,GAAGC,UAAUH,OAAOC,aAAaE,SAASH,MAAMI,MAA9B;EAA7B;;AAGT,IAAMC,4CAAOlH;AACb,IAAMmH,4CAAO/C;;;AC7Ub,IAAAgD,SAAuB;;;ACAvB,YAAuB;;;ACAhB,IAAI,YAAY,oBAAoB;;;ADI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,iBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,aAAO,IAAI;AAC3B,MAAI,KAAW,eAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,eAAe,SAAS,kBAAkB,IAAI,CAAC;AACtgB,MAAI,UAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,oBAAc,SAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,IAAI,CAAC;AAAA,IACrO,eAAsB,mBAAmB,eAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,oBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AEjCA,IAAAC,SAAuB;;;ACDvB,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AAC/D,UAAI,IAAI,GAAG;AACP,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,SAAS;AACzC,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,aAAS,OAAO;AAAA,EACpB;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAClE,MAAI,oBAAqB,gBAAgB,oBAAoB,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AAC5G,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,uBAAuB,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AAChG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;AFzFO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,cAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,cAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,cAAO;AAC9B,MAAI,KAAW,gBAAS,WAAW,EAAE,CAAC;AACtC,MAAI,QAAc,gBAAS,WAAY;AAAE,WAAO,eAAe;AAAA,EAAG,CAAC,EAAE,CAAC;AACtE,MAAI,YAAkB,cAAO,KAAK;AAClC,EAAM,iBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,iBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,mBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAI,aAAa,SAAS,MAAM,QAAQ,WAAW,GAAG;AAClD,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,mBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAM,OAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,QAAQ,EAAE,WAAW,MAAM,UAAU,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAElK,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,mBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,OAAe;AACvE,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,mBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,mBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,mBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,WAAY;AACxB,cAAU,KAAK,KAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAAS;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,qBAAc,OAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,qBAAc,iBAAiB,EAAE,SAAS,SAAS,CAAC,IAAI;AAAA,EAAI;AAC5F;;;AG9IA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ANC3D,IAAI,oBAA0B,kBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,qBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AQqBf,IAAMC,uCAAiB;EAAC;EAAS;;AACjC,IAAMC,mCAAa;EAAC;EAAa;EAAU;;AAC3C,IAAMC,kCAAY;EAAC;EAAW;EAAY;;AAC1C,IAAMC,wCAAkB;KAAIF;KAAeC;;AAC3C,IAAME,sCAA6C;EACjDC,KAAK;OAAIL;IAAgB;;EACzBM,KAAK;OAAIN;IAAgB;;;AAE3B,IAAMO,uCAA8C;EAClDF,KAAK;IAAC;;EACNC,KAAK;IAAC;;;AAOR,IAAME,kCAAY;AAGlB,IAAM,CAACC,kCAAYC,qCAAeC,2CAA5B,IAAqDC,0CAGzDJ,+BAHyE;AAM3E,IAAM,CAACK,yCAAmBC,yCAApB,IAAuCC,yCAAmBP,iCAAW;EACzEG;EACAK;EACAC;CAH6D;AAK/D,IAAMC,uCAAiBF,wCAAiB;AACxC,IAAMG,iDAA2BF,yCAA2B;AAS5D,IAAM,CAACG,oCAAcC,oCAAf,IAAiCR,wCAAoCL,+BAAnB;AASxD,IAAM,CAACc,wCAAkBC,wCAAnB,IAAyCV,wCAAwCL,+BAAvB;AAUhE,IAAMgB,4CAA6BC,CAAAA,UAAkC;AACnE,QAAM,EAAA,aAAA,OAAsB,OAAtB,UAAA,KAAA,cAAA,QAAkE,KAARC,IAAiBD;AACjF,QAAME,cAAcT,qCAAeU,WAAD;AAClC,QAAM,CAACC,SAASC,UAAV,QAAwBC,eAAAA,UAA0C,IAA1C;AAC9B,QAAMC,yBAAqBD,eAAAA,QAAa,KAAb;AAC3B,QAAME,mBAAmBC,0CAAeC,YAAD;AACvC,QAAMC,YAAYC,0CAAaC,GAAD;AAE9BP,qBAAAA,WAAgB,MAAM;AAGpB,UAAMQ,gBAAgB,MAAM;AAC1BP,yBAAmBQ,UAAU;AAC7BC,eAASC,iBAAiB,eAAeC,eAAe;QAAEC,SAAS;QAAMC,MAAM;OAA/E;AACAJ,eAASC,iBAAiB,eAAeC,eAAe;QAAEC,SAAS;QAAMC,MAAM;OAA/E;;AAEF,UAAMF,gBAAgB,MAAOX,mBAAmBQ,UAAU;AAC1DC,aAASC,iBAAiB,WAAWH,eAAe;MAAEK,SAAS;KAA/D;AACA,WAAO,MAAM;AACXH,eAASK,oBAAoB,WAAWP,eAAe;QAAEK,SAAS;OAAlE;AACAH,eAASK,oBAAoB,eAAeH,eAAe;QAAEC,SAAS;OAAtE;AACAH,eAASK,oBAAoB,eAAeH,eAAe;QAAEC,SAAS;OAAtE;;KAED,CAAA,CAfH;AAiBA,aACE,eAAAG,eAAC,2CAAyBpB,iBACxB,eAAAoB,eAAC,oCAFL;IAGM,OAAOnB;IACP;IACA,cAAcK;IACd;IACA,iBAAiBH;SAEjB,eAAAiB,eAAC,wCAPH;IAQI,OAAOnB;IACP,aAASG,eAAAA;MAAkB,MAAME,iBAAiB,KAAD;MAAS;QAACA;;IAAlD;IACT;IACA,KAAKG;IACL;KAECY,QAPH,CAPF,CADF;;AAsBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,oCAAc;AAMpB,IAAMC,gDAAanB,eAAAA,YACjB,CAACN,OAAqC0B,iBAAiB;AACrD,QAAM,EAAA,aAAe,GAAGC,YAAH,IAAmB3B;AACxC,QAAME,cAAcT,qCAAeU,WAAD;AAClC,aAAO,eAAAmB,eAAC,2CAAD,SAAA,CAAA,GAA4BpB,aAAiByB,aAApD;IAAiE,KAAKD;GAA/D,CAAA;CAJQ;AAQnB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAME,oCAAc;AAGpB,IAAM,CAACC,sCAAgBC,sCAAjB,IAAqC1C,wCAAsCwC,mCAAa;EAC5FG,YAAYC;CAD8C;AAkB5D,IAAMC,4CAAyCjC,CAAAA,UAAwC;AACrF,QAAM,EAAA,aAAA,YAAA,UAAA,UAAqCkC,IAAclC;AACzD,QAAMmC,UAAUvC,qCAAegC,mCAAazB,WAAd;AAC9B,aACE,eAAAmB,eAAC,sCADH;IACkB,OAAOnB;IAAa;SAClC,eAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,eAAAd,eAAC,2CADH;IACmB,SAAO;IAAC;KACtBC,QADH,CADF,CADF;;AAUJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMc,qCAAe;AAUrB,IAAM,CAACC,2CAAqBC,2CAAtB,IACJnD,wCAA2CiD,kCAA1B;AAgBnB,IAAMG,gDAAclC,eAAAA,YAClB,CAACN,OAAsC0B,iBAAiB;AACtD,QAAMe,gBAAgBX,uCAAiBO,oCAAcrC,MAAMG,WAArB;AACtC,QAAM,EAAA,aAAesC,cAAcV,YAAY,GAAGW,aAAH,IAAoB1C;AACnE,QAAMmC,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAcrC,MAAMG,WAArB;AAEtC,aACE,eAAAmB,eAAC,iCAAW,UADd;IACuB,OAAOtB,MAAMG;SAChC,eAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,eAAAd,eAAC,iCAAW,MADd;IACmB,OAAOtB,MAAMG;KAC3BwC,YAAY1C,YACX,eAAAqB,eAAC,4CAAD,SAAA,CAAA,GAA0BoB,cAF9B;IAE4C,KAAKhB;GAA7C,CAAA,QAEA,eAAAJ,eAAC,+CAAD,SAAA,CAAA,GAA6BoB,cAF7B;IAE2C,KAAKhB;GAAhD,CAAA,CAJJ,CADF,CADF;CARc;AA6BpB,IAAMkB,iDAAuBtC,eAAAA,YAC3B,CAACN,OAA8C0B,iBAAiB;AAC9D,QAAMS,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAM0C,UAAMvC,eAAAA,QAAyC,IAAzC;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AAGpCvC,qBAAAA,WAAgB,MAAM;AACpB,UAAMF,UAAUyC,IAAI9B;AACpB,QAAIX;AAAS,aAAO4C,WAAW5C,OAAD;KAC7B,CAAA,CAHH;AAKA,aACE,eAAAkB,eAAC,uCAAD,SAAA,CAAA,GACMtB,OAFR;IAGI,KAAK8C;IAGL,WAAWX,QAAQC;IAGnB,6BAA6BD,QAAQC;IACrC,sBAAoB;IAGpB,gBAAgBa;MACdjD,MAAMkD;MACLC,CAAAA,UAAUA,MAAMC,eAAN;MACX;QAAEC,0BAA0B;;IAHM;IAKpC,WAAW,MAAMlB,QAAQzB,aAAa,KAArB;GAjBnB,CAAA;CAbuB;AAoC7B,IAAM4C,oDAA0BhD,eAAAA,YAG9B,CAACN,OAA8C0B,iBAAiB;AAChE,QAAMS,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,aACE,eAAAmB,eAAC,uCAAD,SAAA,CAAA,GACMtB,OAFR;IAGI,KAAK0B;IACL,WAAW;IACX,6BAA6B;IAC7B,sBAAsB;IACtB,WAAW,MAAMS,QAAQzB,aAAa,KAArB;GANnB,CAAA;CAN4B;AA+DhC,IAAM6C,4CAAkBjD,eAAAA,YACtB,CAACN,OAA0C0B,iBAAiB;AAC1D,QAAM,EAAA,aAAA,OAEG,OAFH,WAAA,iBAAA,kBAAA,6BAAA,cAAA,iBAAA,sBAAA,gBAAA,mBAAA,WAAA,sBAcJ,GAAGgB,aAAH,IACE1C;AACJ,QAAMmC,UAAUvC,qCAAeyC,oCAAclC,WAAf;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAclC,WAAf;AACtC,QAAMD,cAAcT,qCAAeU,WAAD;AAClC,QAAMqD,wBAAwB9D,+CAAyBS,WAAD;AACtD,QAAMsD,WAAWxE,oCAAckB,WAAD;AAC9B,QAAM,CAACuD,eAAeC,gBAAhB,QAAoCrD,eAAAA,UAA8B,IAA9B;AAC1C,QAAMsD,iBAAatD,eAAAA,QAA6B,IAA7B;AACnB,QAAMwC,eAAeC,0CAAgBrB,cAAckC,YAAYzB,QAAQ0B,eAAnC;AACpC,QAAMC,eAAWxD,eAAAA,QAAa,CAAb;AACjB,QAAMyD,gBAAYzD,eAAAA,QAAa,EAAb;AAClB,QAAM0D,2BAAuB1D,eAAAA,QAAa,CAAb;AAC7B,QAAM2D,4BAAwB3D,eAAAA,QAAiC,IAAjC;AAC9B,QAAM4D,oBAAgB5D,eAAAA,QAAmB,OAAnB;AACtB,QAAM6D,sBAAkB7D,eAAAA,QAAa,CAAb;AAExB,QAAM8D,oBAAoBC,uBAAuBC,sBAAehE,eAAAA;AAChE,QAAMiE,yBAAyBF,uBAC3B;IAAEG,IAAIC;IAAMC,gBAAgB;MAC5B1C;AAEJ,QAAM2C,wBAAyBC,CAAAA,QAAgB;AAAA,QAAA,aAAA;AAC7C,UAAMC,SAASd,UAAUhD,UAAU6D;AACnC,UAAME,QAAQrB,SAAQ,EAAGsB;MAAQC,CAAAA,SAAS,CAACA,KAAKC;IAAlC;AACd,UAAMC,cAAclE,SAASmE;AAC7B,UAAMC,gBAAY,cAAGN,MAAMO;MAAML,CAAAA,SAASA,KAAKnC,IAAI9B,YAAYmE;IAA1C,OAAH,QAAA,gBAAA,SAAA,SAAG,YAAwDI;AAC7E,UAAMC,SAAST,MAAMU;MAAKR,CAAAA,SAASA,KAAKM;IAAzB;AACf,UAAMG,YAAYC,mCAAaH,QAAQV,QAAQO,YAAjB;AAC9B,UAAMO,WAAO,eAAGb,MAAMO;MAAML,CAAAA,SAASA,KAAKM,cAAcG;IAAxC,OAAH,QAAA,iBAAA,SAAA,SAAG,aAAoD5C,IAAI9B;AAGvE,KAAA,SAAS6E,aAAaC,OAAe;AACpC9B,gBAAUhD,UAAU8E;AACpBC,aAAOC,aAAajC,SAAS/C,OAA7B;AACA,UAAI8E,UAAU;AAAI/B,iBAAS/C,UAAU+E,OAAOE;UAAW,MAAMJ,aAAa,EAAD;UAAM;QAA1C;OACpCf,MAJH;AAMA,QAAIc;AAKFK;QAAW,MAAOL,QAAwBM,MAAzB;MAAP;;AAId3F,qBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAMwF,OAAOC,aAAajC,SAAS/C,OAA7B;KACZ,CAAA,CAFH;AAMAmF,4CAAc;AAEd,QAAMC,+BAA2B7F,eAAAA,aAAmB6C,CAAAA,UAA8B;AAAA,QAAA,uBAAA;AAChF,UAAMiD,kBAAkBlC,cAAcnD,cAAd,wBAA0BkD,sBAAsBlD,aAAhD,QAAA,0BAAA,SAAA,SAA0B,sBAA+BsF;AACjF,WAAOD,mBAAmBE,2CAAqBnD,QAAD,yBAAQc,sBAAsBlD,aAA9B,QAAA,2BAAA,SAAA,SAAQ,uBAA+BwF,IAAvC;KAC7C,CAAA,CAH8B;AAKjC,aACE,eAAAjF,eAAC,2CADH;IAEI,OAAOnB;IACP;IACA,iBAAaG,eAAAA,aACV6C,CAAAA,UAAU;AACT,UAAIgD,yBAAyBhD,KAAD;AAASA,cAAMC,eAAN;OAEvC;MAAC+C;KAJU;IAMb,iBAAa7F,eAAAA,aACV6C,CAAAA,UAAU;AAAA,UAAA;AACT,UAAIgD,yBAAyBhD,KAAD;AAAS;AACrC,OAAA,sBAAAS,WAAW7C,aAAX,QAAA,wBAAA,UAAA,oBAAoBkF,MAApB;AACAtC,uBAAiB,IAAD;OAElB;MAACwC;KANU;IAQb,oBAAgB7F,eAAAA,aACb6C,CAAAA,UAAU;AACT,UAAIgD,yBAAyBhD,KAAD;AAASA,cAAMC,eAAN;OAEvC;MAAC+C;KAJa;IAMhB;IACA,gCAA4B7F,eAAAA,aAAmBkG,CAAAA,WAAW;AACxDvC,4BAAsBlD,UAAUyF;OAC/B,CAAA,CAFyB;SAI5B,eAAAlF,eAAC,mBAAsBiD,4BACrB,eAAAjD,eAAC,2CA7BL;IA8BM,SAAO;IACP,SAASmF;IACT,kBAAkBxD,0CAAqByD,iBAAkBvD,CAAAA,UAAU;AAAA,UAAA;AAGjEA,YAAMC,eAAN;AACA,OAAA,uBAAAQ,WAAW7C,aAAX,QAAA,yBAAA,UAAA,qBAAoBkF,MAApB;KAJoC;IAMtC,oBAAoBU;SAEpB,eAAArF,eAAC,2CAXH;IAYI,SAAO;IACP;IACA;IACA;IACA;IACA;IACA;SAEA,eAAAA,eAAC,2CATH,SAAA;IAUI,SAAA;KACIkC,uBAFN;IAGE,KAAKb,YAAY9B;IACjB,aAAY;IACZ;IACA,kBAAkB6C;IAClB,0BAA0BC;IAC1B,cAAcV,0CAAqB2D,cAAezD,CAAAA,UAAU;AAE1D,UAAI,CAACR,YAAYpC,mBAAmBQ;AAASoC,cAAMC,eAAN;KAFb;GARpC,OAaE,eAAA9B,eAAC,2CAbH,SAAA;IAcI,MAAK;IACL,oBAAiB;IACjB,cAAYuF,mCAAa1E,QAAQC,IAAT;IACxB,2BAAwB;IACxB,KAAKO,YAAY9B;KACbX,aACAwC,cAPN;IAQE,KAAKI;IACL,OAAO;MAAEgE,SAAS;MAAQ,GAAGpE,aAAaqE;;IAC1C,WAAW9D,0CAAqBP,aAAasE,WAAY7D,CAAAA,UAAU;AAEjE,YAAM8D,SAAS9D,MAAM8D;AACrB,YAAMC,kBACJD,OAAOE,QAAQ,2BAAf,MAAgDhE,MAAMiE;AACxD,YAAMC,gBAAgBlE,MAAMmE,WAAWnE,MAAMoE,UAAUpE,MAAMqE;AAC7D,YAAMC,iBAAiBtE,MAAMyB,IAAI8C,WAAW;AAC5C,UAAIR,iBAAiB;AAEnB,YAAI/D,MAAMyB,QAAQ;AAAOzB,gBAAMC,eAAN;AACzB,YAAI,CAACiE,iBAAiBI;AAAgB9C,gCAAsBxB,MAAMyB,GAAP;;AAG7D,YAAMxE,UAAUwD,WAAW7C;AAC3B,UAAIoC,MAAM8D,WAAW7G;AAAS;AAC9B,UAAI,CAAC1B,sCAAgBiJ,SAASxE,MAAMyB,GAA/B;AAAqC;AAC1CzB,YAAMC,eAAN;AACA,YAAM0B,QAAQrB,SAAQ,EAAGsB;QAAQC,CAAAA,SAAS,CAACA,KAAKC;MAAlC;AACd,YAAM2C,iBAAiB9C,MAAMU;QAAKR,CAAAA,SAASA,KAAKnC,IAAI9B;MAA7B;AACvB,UAAItC,gCAAUkJ,SAASxE,MAAMyB,GAAzB;AAA+BgD,uBAAeC,QAAf;AACnCC,uCAAWF,cAAD;KApBmB;IAsB/B,QAAQ3E,0CAAqBjD,MAAM+H,QAAS5E,CAAAA,UAAU;AAEpD,UAAI,CAACA,MAAMiE,cAAcY,SAAS7E,MAAM8D,MAAnC,GAA4C;AAC/CnB,eAAOC,aAAajC,SAAS/C,OAA7B;AACAgD,kBAAUhD,UAAU;;KAJI;IAO5B,eAAekC,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB,YAAM8D,SAAS9D,MAAM8D;AACrB,YAAMkB,qBAAqBhE,gBAAgBpD,YAAYoC,MAAMiF;AAI7D,UAAIjF,MAAMiE,cAAcY,SAASf,MAA7B,KAAwCkB,oBAAoB;AAC9D,cAAME,SAASlF,MAAMiF,UAAUjE,gBAAgBpD,UAAU,UAAU;AACnEmD,sBAAcnD,UAAUsH;AACxBlE,wBAAgBpD,UAAUoC,MAAMiF;;KAT3B,CAFwB;GAvCrC,CAAA,CAbF,CATF,CAXF,CADF,CA5BF;CA7EkB;AA2MxB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAME,mCAAa;AAMnB,IAAMC,gDAAYjI,eAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAG8G,WAAH,IAAkBxI;AACvC,aAAO,eAAAsB,eAAC,0CAAU,KAAlB,SAAA;IAAsB,MAAK;KAAYkH,YAAhC;IAA4C,KAAK9G;GAAjD,CAAA;CAHO;AAOlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM+G,mCAAa;AAKnB,IAAMC,gDAAYpI,eAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAGiH,WAAH,IAAkB3I;AACvC,aAAO,eAAAsB,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBqH,YAA1B;IAAsC,KAAKjH;GAApC,CAAA;CAHO;AAOlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkH,kCAAY;AAClB,IAAMC,oCAAc;AAOpB,IAAMC,gDAAWxI,eAAAA,YACf,CAACN,OAAmC0B,iBAAiB;AACnD,QAAM,EAAA,WAAa,OAAb,UAA8B,GAAGqH,UAAH,IAAiB/I;AACrD,QAAM6C,UAAMvC,eAAAA,QAA6B,IAA7B;AACZ,QAAMqC,cAAc7C,yCAAmB8I,iCAAW5I,MAAMG,WAAlB;AACtC,QAAM6I,iBAAiBzG,4CAAsBqG,iCAAW5I,MAAMG,WAAlB;AAC5C,QAAM2C,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,QAAMoG,uBAAmB3I,eAAAA,QAAa,KAAb;AAEzB,QAAM4I,eAAe,MAAM;AACzB,UAAMC,WAAWtG,IAAI9B;AACrB,QAAI,CAACkE,YAAYkE,UAAU;AACzB,YAAMC,kBAAkB,IAAIC,YAAYR,mCAAa;QAAES,SAAS;QAAMC,YAAY;OAA1D;AACxBJ,eAASlI;QAAiB4H;QAAc1F,CAAAA,UAAUqG,aAAX,QAAWA,aAAX,SAAA,SAAWA,SAAWrG,KAAH;QAAW;UAAE/B,MAAM;;MAA7E;AACAqI,gDAA4BN,UAAUC,eAAX;AAC3B,UAAIA,gBAAgBM;AAClBT,yBAAiBlI,UAAU;;AAE3B4B,oBAAYgH,QAAZ;;;AAKN,aACE,eAAArI,eAAC,oCAAD,SAAA,CAAA,GACMyH,WAFR;IAGI,KAAKjG;IACL;IACA,SAASG,0CAAqBjD,MAAM4J,SAASV,YAAhB;IAC7B,eAAgB/F,CAAAA,UAAU;AAAA,UAAA;AACxB,OAAA,uBAAAnD,MAAM6J,mBAAN,QAAA,yBAAA,UAAA,qBAAA,KAAA7J,OAAsBmD,KAAjB;AACL8F,uBAAiBlI,UAAU;;IAE7B,aAAakC,0CAAqBjD,MAAM8J,aAAc3G,CAAAA,UAAU;AAAA,UAAA;AAI9D,UAAI,CAAC8F,iBAAiBlI;AAAS,SAAA,uBAAAoC,MAAMiE,mBAAN,QAAA,yBAAA,UAAA,qBAAqB2C,MAArB;KAJA;IAMjC,WAAW9G,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAC1D,YAAM6G,gBAAgBhB,eAAejF,UAAUhD,YAAY;AAC3D,UAAIkE,YAAa+E,iBAAiB7G,MAAMyB,QAAQ;AAAM;AACtD,UAAIrG,qCAAeoJ,SAASxE,MAAMyB,GAA9B,GAAoC;AACtCzB,cAAMiE,cAAc2C,MAApB;AAOA5G,cAAMC,eAAN;;KAX2B;GAfjC,CAAA;CAxBW;AA0DjB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAUA,IAAM6G,yCAAe3J,eAAAA,YACnB,CAACN,OAAuC0B,iBAAiB;AACvD,QAAM,EAAA,aAAA,WAA0B,OAA1B,WAA4C,GAAGqH,UAAH,IAAiB/I;AACnE,QAAMgJ,iBAAiBzG,4CAAsBqG,iCAAWzI,WAAZ;AAC5C,QAAMqD,wBAAwB9D,+CAAyBS,WAAD;AACtD,QAAM0C,UAAMvC,eAAAA,QAA6B,IAA7B;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,QAAM,CAACqH,WAAWC,YAAZ,QAA4B7J,eAAAA,UAAe,KAAf;AAGlC,QAAM,CAAC8J,aAAaC,cAAd,QAAgC/J,eAAAA,UAAe,EAAf;AACtCA,qBAAAA,WAAgB,MAAM;AACpB,UAAM6I,WAAWtG,IAAI9B;AACrB,QAAIoI,UAAU;AAAA,UAAA;AACZkB,uBAAe,wBAAClB,SAASiB,iBAAV,QAAA,0BAAA,SAAA,wBAAyB,IAAIE,KAA7B,CAAD;;KAEf;IAACvB,UAAUxH;GALd;AAOA,aACE,eAAAD,eAAC,iCAAW,UADd;IAEI,OAAOnB;IACP;IACA,WAAWmF,cAAF,QAAEA,cAAF,SAAEA,YAAa8E;SAExB,eAAA9I,eAAC,2CALH,SAAA;IAKyB,SAAA;KAAYkC,uBAAnC;IAA0D,WAAW,CAACyB;GAAtE,OACE,eAAA3D,eAAC,0CAAU,KADb,SAAA;IAEI,MAAK;IACL,oBAAkB4I,YAAY,KAAKlI;IACnC,iBAAeiD,YAAYjD;IAC3B,iBAAeiD,WAAW,KAAKjD;KAC3B+G,WALN;IAME,KAAKjG;IAYL,eAAeG,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB,UAAI8B;AACF+D,uBAAeuB,YAAYpH,KAA3B;WACK;AACL6F,uBAAewB,YAAYrH,KAA3B;AACA,YAAI,CAACA,MAAMuG,kBAAkB;AAC3B,gBAAM1E,OAAO7B,MAAMiE;AACnBpC,eAAKiB,MAAL;;;KAPG,CAFwB;IAcnC,gBAAgBhD,0CACdjD,MAAMyK,gBACNvC;MAAW/E,CAAAA,UAAU6F,eAAeuB,YAAYpH,KAA3B;IAAZ,CAFyB;IAIpC,SAASF;MAAqBjD,MAAM0K;MAAS,MAAMP,aAAa,IAAD;IAAlC;IAC7B,QAAQlH;MAAqBjD,MAAM+H;MAAQ,MAAMoC,aAAa,KAAD;IAAjC;GArC9B,CAAA,CADF,CALF;CAnBe;AA0ErB,IAAMQ,2CAAqB;AAY3B,IAAMC,gDAAmBtK,eAAAA,YACvB,CAACN,OAA2C0B,iBAAiB;AAC3D,QAAM,EAAA,UAAY,OAAZ,iBAAoC,GAAGmJ,kBAAH,IAAyB7K;AACnE,aACE,eAAAsB,eAAC,6CADH;IACyB,OAAOtB,MAAMG;IAAa;SAC/C,eAAAmB,eAAC,2CADH,SAAA;IAEI,MAAK;IACL,gBAAcwJ,sCAAgBC,OAAD,IAAY,UAAUA;KAC/CF,mBAHN;IAIE,KAAKnJ;IACL,cAAYsJ,sCAAgBD,OAAD;IAC3B,UAAU9H;MACR4H,kBAAkBrB;MAClB,MAAMyB,oBAAN,QAAMA,oBAAN,SAAA,SAAMA,gBAAkBH,sCAAgBC,OAAD,IAAY,OAAO,CAACA,OAAtC;MACrB;QAAE1H,0BAA0B;;IAHA;GANhC,CAAA,CADF;CAJmB;AAsBzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM6H,yCAAmB;AAEzB,IAAM,CAACC,0CAAoBC,0CAArB,IAA6ChM,wCACjD8L,wCACA;EAAErF,OAAO7D;EAAWqJ,eAAe,MAAM;EAAA;CAFyB;AAWpE,IAAMC,gDAAiBhL,eAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAM,EAAA,OAAA,eAAwB,GAAG8G,WAAH,IAAkBxI;AAChD,QAAMuL,oBAAoB9K,0CAAe4K,aAAD;AACxC,aACE,eAAA/J,eAAC,0CADH;IACsB,OAAOtB,MAAMG;IAAa;IAAc,eAAeoL;SACzE,eAAAjK,eAAC,2CAAD,SAAA,CAAA,GAAekH,YADjB;IAC6B,KAAK9G;GAAhC,CAAA,CADF;CALiB;AAYvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM8J,wCAAkB;AAOxB,IAAMC,gDAAgBnL,eAAAA,YACpB,CAACN,OAAwC0B,iBAAiB;AACxD,QAAM,EAAA,OAAS,GAAGgK,eAAH,IAAsB1L;AACrC,QAAMmC,UAAUiJ,2CAAqBI,uCAAiBxL,MAAMG,WAAxB;AACpC,QAAM4K,UAAUlF,UAAU1D,QAAQ0D;AAClC,aACE,eAAAvE,eAAC,6CADH;IACyB,OAAOtB,MAAMG;IAAa;SAC/C,eAAAmB,eAAC,2CADH,SAAA;IAEI,MAAK;IACL,gBAAcyJ;KACVW,gBAHN;IAIE,KAAKhK;IACL,cAAYsJ,sCAAgBD,OAAD;IAC3B,UAAU9H,0CACRyI,eAAelC,UACf,MAFF;AAEE,UAAA;AAAA,cAAA,wBAAMrH,QAAQkJ,mBAAd,QAAA,0BAAA,SAAA,SAAM,sBAAA,KAAAlJ,SAAwB0D,KAAjB;OACb;MAAExC,0BAA0B;KAHA;GANhC,CAAA,CADF;CANgB;AAwBtB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMsI,4CAAsB;AAI5B,IAAM,CAACC,6CAAuBC,6CAAxB,IAAmDzM,wCACvDuM,2CACA;EAAEZ,SAAS;CAF6D;AAe1E,IAAMe,gDAAoBxL,eAAAA,YACxB,CAACN,OAA4C0B,iBAAiB;AAC5D,QAAM,EAAA,aAAA,YAA2B,GAAGqK,mBAAH,IAA0B/L;AAC3D,QAAMgM,mBAAmBH,8CAAwBF,2CAAqBxL,WAAtB;AAChD,aACE,eAAAmB,eAAC,2CADH;IAEI,SACES,cACA+I,sCAAgBkB,iBAAiBjB,OAAlB,KACfiB,iBAAiBjB,YAAY;SAG/B,eAAAzJ,eAAC,0CAAU,MAAX,SAAA,CAAA,GACMyK,oBARR;IASI,KAAKrK;IACL,cAAYsJ,sCAAgBgB,iBAAiBjB,OAAlB;GAH7B,CAAA,CAPF;CALoB;AAsB1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMkB,uCAAiB;AAKvB,IAAMC,gDAAgB5L,eAAAA,YACpB,CAACN,OAAwC0B,iBAAiB;AACxD,QAAM,EAAA,aAAe,GAAGyK,eAAH,IAAsBnM;AAC3C,aACE,eAAAsB,eAAC,0CAAU,KADb,SAAA;IAEI,MAAK;IACL,oBAAiB;KACb6K,gBAHN;IAIE,KAAKzK;GAJP,CAAA;CAJgB;AActB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM0K,mCAAa;AAMnB,IAAMC,gDAAY/L,eAAAA,YAChB,CAACN,OAAoC0B,iBAAiB;AACpD,QAAM,EAAA,aAAe,GAAG4K,WAAH,IAAkBtM;AACvC,QAAME,cAAcT,qCAAeU,WAAD;AAClC,aAAO,eAAAmB,eAAC,2CAAD,SAAA,CAAA,GAA2BpB,aAAiBoM,YAAnD;IAA+D,KAAK5K;GAA7D,CAAA;CAJO;AAQlB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM6K,iCAAW;AASjB,IAAM,CAACC,uCAAiBC,uCAAlB,IAAuCrN,wCAAuCmN,8BAAtB;AAQ9D,IAAMG,4CAAmC1M,CAAAA,UAAqC;AAC5E,QAAM,EAAA,aAAA,UAAA,OAAgC,OAAhC,aAAuCU,IAAiBV;AAC9D,QAAM2M,oBAAoB/M,qCAAe2M,gCAAUpM,WAAX;AACxC,QAAMD,cAAcT,qCAAeU,WAAD;AAClC,QAAM,CAACyM,SAASC,UAAV,QAAwBvM,eAAAA,UAA6C,IAA7C;AAC9B,QAAM,CAACF,SAASC,UAAV,QAAwBC,eAAAA,UAA0C,IAA1C;AAC9B,QAAME,mBAAmBC,0CAAeC,YAAD;AAGvCJ,qBAAAA,WAAgB,MAAM;AACpB,QAAIqM,kBAAkBvK,SAAS;AAAO5B,uBAAiB,KAAD;AACtD,WAAO,MAAMA,iBAAiB,KAAD;KAC5B;IAACmM,kBAAkBvK;IAAM5B;GAH5B;AAKA,aACE,eAAAc,eAAC,2CAAyBpB,iBACxB,eAAAoB,eAAC,oCAFL;IAGM,OAAOnB;IACP;IACA,cAAcK;IACd;IACA,iBAAiBH;SAEjB,eAAAiB,eAAC,uCAPH;IAQI,OAAOnB;IACP,WAAW2M,0CAAK;IAChB,WAAWA,0CAAK;IAChB;IACA,iBAAiBD;KAEhBtL,QAPH,CAPF,CADF;;AAsBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMwL,yCAAmB;AAKzB,IAAMC,gDAAiB1M,eAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAMS,UAAUvC,qCAAemN,wCAAkB/M,MAAMG,WAAzB;AAC9B,QAAMwC,cAAc7C,yCAAmBiN,wCAAkB/M,MAAMG,WAAzB;AACtC,QAAM8M,aAAaR,wCAAkBM,wCAAkB/M,MAAMG,WAAzB;AACpC,QAAM6I,iBAAiBzG,4CAAsBwK,wCAAkB/M,MAAMG,WAAzB;AAC5C,QAAM+M,mBAAe5M,eAAAA,QAA4B,IAA5B;AACrB,QAAM,EAAA,sBAAA,2BAAwB6M,IAA+BnE;AAC7D,QAAMoE,QAAQ;IAAEjN,aAAaH,MAAMG;;AAEnC,QAAMkN,qBAAiB/M,eAAAA,aAAkB,MAAM;AAC7C,QAAI4M,aAAanM;AAAS+E,aAAOC,aAAamH,aAAanM,OAAjC;AAC1BmM,iBAAanM,UAAU;KACtB,CAAA,CAHoB;AAKvBT,qBAAAA;IAAgB,MAAM+M;IAAgB;MAACA;;EAAvC;AAEA/M,qBAAAA,WAAgB,MAAM;AACpB,UAAMgN,oBAAoBtJ,qBAAqBjD;AAC/C,WAAO,MAAM;AACX+E,aAAOC,aAAauH,iBAApB;AACAH,iCAA2B,IAAD;;KAE3B;IAACnJ;IAAsBmJ;GAN1B;AAQA,aACE,eAAA7L,eAAC,2CADH,SAAA;IACc,SAAA;KAAY8L,KAAxB,OACE,eAAA9L,eAAC,oCADH,SAAA;IAEI,IAAI2L,WAAWM;IACf,iBAAc;IACd,iBAAepL,QAAQC;IACvB,iBAAe6K,WAAWO;IAC1B,cAAY3G,mCAAa1E,QAAQC,IAAT;KACpBpC,OANN;IAOE,KAAKyN,0CAAY/L,cAAcuL,WAAWS,eAA1B;IAGhB,SAAUvK,CAAAA,UAAU;AAAA,UAAA;AAClB,OAAA,iBAAAnD,MAAM4J,aAAN,QAAA,mBAAA,UAAA,eAAA,KAAA5J,OAAgBmD,KAAX;AACL,UAAInD,MAAMiF,YAAY9B,MAAMuG;AAAkB;AAM9CvG,YAAMiE,cAAcnB,MAApB;AACA,UAAI,CAAC9D,QAAQC;AAAMD,gBAAQzB,aAAa,IAArB;;IAErB,eAAeuC,0CACbjD,MAAMiI,eACNC,gCAAW/E,CAAAA,UAAU;AACnB6F,qBAAewB,YAAYrH,KAA3B;AACA,UAAIA,MAAMuG;AAAkB;AAC5B,UAAI,CAAC1J,MAAMiF,YAAY,CAAC9C,QAAQC,QAAQ,CAAC8K,aAAanM,SAAS;AAC7DiI,uBAAemE,2BAA2B,IAA1C;AACAD,qBAAanM,UAAU+E,OAAOE,WAAW,MAAM;AAC7C7D,kBAAQzB,aAAa,IAArB;AACA2M,yBAAc;WACb,GAHoB;;KALlB,CAFwB;IAcnC,gBAAgBpK,0CACdjD,MAAMyK,gBACNvC,gCAAW/E,CAAAA,UAAU;AAAA,UAAA;AACnBkK,qBAAc;AAEd,YAAMM,eAAW,mBAAGxL,QAAQ/B,aAAX,QAAA,qBAAA,SAAA,SAAG,iBAAiBwN,sBAAjB;AACpB,UAAID,aAAa;AAAA,YAAA;AAEf,cAAMtH,QAAI,oBAAGlE,QAAQ/B,aAAX,QAAA,sBAAA,SAAA,SAAG,kBAAiByN,QAAQxH;AACtC,cAAMyH,YAAYzH,SAAS;AAC3B,cAAM0H,QAAQD,YAAY,KAAK;AAC/B,cAAME,kBAAkBL,YAAYG,YAAY,SAAS,OAAtB;AACnC,cAAMG,iBAAiBN,YAAYG,YAAY,UAAU,MAAvB;AAElC9E,uBAAemE,2BAA2B;UACxC5G,MAAM;;YAGJ;cAAE2H,GAAG/K,MAAMiF,UAAU2F;cAAOI,GAAGhL,MAAMiL;;YACrC;cAAEF,GAAGF;cAAiBG,GAAGR,YAAYU;;YACrC;cAAEH,GAAGD;cAAgBE,GAAGR,YAAYU;;YACpC;cAAEH,GAAGD;cAAgBE,GAAGR,YAAYW;;YACpC;cAAEJ,GAAGF;cAAiBG,GAAGR,YAAYW;;;;SARzC;AAaAxI,eAAOC,aAAa/B,qBAAqBjD,OAAzC;AACAiD,6BAAqBjD,UAAU+E,OAAOE;UACpC,MAAMgD,eAAemE,2BAA2B,IAA1C;UACN;QAF6B;aAI1B;AACLnE,uBAAeuF,eAAepL,KAA9B;AACA,YAAIA,MAAMuG;AAAkB;AAG5BV,uBAAemE,2BAA2B,IAA1C;;KAnCK,CAFyB;IAyCpC,WAAWlK,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAC1D,YAAM6G,gBAAgBhB,eAAejF,UAAUhD,YAAY;AAC3D,UAAIf,MAAMiF,YAAa+E,iBAAiB7G,MAAMyB,QAAQ;AAAM;AAC5D,UAAIjG,oCAAcgE,YAAY9B,GAAb,EAAkB8G,SAASxE,MAAMyB,GAA9C,GAAoD;AAAA,YAAA;AACtDzC,gBAAQzB,aAAa,IAArB;AAGA,SAAA,oBAAAyB,QAAQ/B,aAAR,QAAA,sBAAA,UAAA,kBAAiB6F,MAAjB;AAEA9C,cAAMC,eAAN;;KAT2B;GA5EjC,CAAA,CADF;CA1BiB;AAyHvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMoL,yCAAmB;AAezB,IAAMC,gDAAiBnO,eAAAA,YACrB,CAACN,OAAyC0B,iBAAiB;AACzD,QAAMe,gBAAgBX,uCAAiBO,oCAAcrC,MAAMG,WAArB;AACtC,QAAM,EAAA,aAAesC,cAAcV,YAAY,GAAG2M,gBAAH,IAAuB1O;AACtE,QAAMmC,UAAUvC,qCAAeyC,oCAAcrC,MAAMG,WAArB;AAC9B,QAAMwC,cAAc7C,yCAAmBuC,oCAAcrC,MAAMG,WAArB;AACtC,QAAM8M,aAAaR,wCAAkB+B,wCAAkBxO,MAAMG,WAAzB;AACpC,QAAM0C,UAAMvC,eAAAA,QAAoC,IAApC;AACZ,QAAMwC,eAAeC,0CAAgBrB,cAAcmB,GAAf;AACpC,aACE,eAAAvB,eAAC,iCAAW,UADd;IACuB,OAAOtB,MAAMG;SAChC,eAAAmB,eAAC,2CADH;IACY,SAASS,cAAcI,QAAQC;SACvC,eAAAd,eAAC,iCAAW,MADd;IACmB,OAAOtB,MAAMG;SAC5B,eAAAmB,eAAC,uCADH,SAAA;IAEI,IAAI2L,WAAWO;IACf,mBAAiBP,WAAWM;KACxBmB,iBAHN;IAIE,KAAK5L;IACL,OAAM;IACN,MAAMH,YAAY9B,QAAQ,QAAQ,SAAS;IAC3C,6BAA6B;IAC7B,sBAAsB;IACtB,WAAW;IACX,iBAAkBsC,CAAAA,UAAU;AAAA,UAAA;AAE1B,UAAIR,YAAYpC,mBAAmBQ;AAAS,SAAA,eAAA8B,IAAI9B,aAAJ,QAAA,iBAAA,UAAA,aAAakF,MAAb;AAC5C9C,YAAMC,eAAN;;IAIF,kBAAmBD,CAAAA,UAAUA,MAAMC,eAAN;IAC7B,gBAAgBH,0CAAqBjD,MAAMkD,gBAAiBC,CAAAA,UAAU;AAGpE,UAAIA,MAAM8D,WAAWgG,WAAWL;AAASzK,gBAAQzB,aAAa,KAArB;KAHP;IAKpC,iBAAiBuC,0CAAqBjD,MAAM2O,iBAAkBxL,CAAAA,UAAU;AACtER,kBAAYgH,QAAZ;AAEAxG,YAAMC,eAAN;KAHmC;IAKrC,WAAWH,0CAAqBjD,MAAMgH,WAAY7D,CAAAA,UAAU;AAE1D,YAAM+D,kBAAkB/D,MAAMiE,cAAcY,SAAS7E,MAAM8D,MAAnC;AACxB,YAAM2H,aAAa9P,qCAAe6D,YAAY9B,GAAb,EAAkB8G,SAASxE,MAAMyB,GAA/C;AACnB,UAAIsC,mBAAmB0H,YAAY;AAAA,YAAA;AACjCzM,gBAAQzB,aAAa,KAArB;AAEA,SAAA,sBAAAuM,WAAWL,aAAX,QAAA,wBAAA,UAAA,oBAAoB3G,MAApB;AAEA9C,cAAMC,eAAN;;KAT2B;GA5BjC,CAAA,CADF,CADF,CADF;CAViB;AA6DvB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,SAASyD,mCAAazE,MAAe;AACnC,SAAOA,OAAO,SAAS;;AAGzB,SAAS0I,sCAAgBC,SAAoD;AAC3E,SAAOA,YAAY;;AAGrB,SAASC,sCAAgBD,SAAuB;AAC9C,SAAOD,sCAAgBC,OAAD,IAAY,kBAAkBA,UAAU,YAAY;;AAG5E,SAASjD,iCAAW+G,YAA2B;AAC7C,QAAMC,6BAA6B9N,SAASmE;AAC5C,aAAW4J,aAAaF,YAAY;AAElC,QAAIE,cAAcD;AAA4B;AAC9CC,cAAU9I,MAAV;AACA,QAAIjF,SAASmE,kBAAkB2J;AAA4B;;;AAQ/D,SAASE,gCAAaC,OAAYC,YAAoB;AACpD,SAAOD,MAAMzJ;IAAI,CAAC2J,GAAGC,UAAUH,OAAOC,aAAaE,SAASH,MAAMvH,MAA9B;EAA7B;;AAoBT,SAAShC,mCAAaH,QAAkBV,QAAgBO,cAAuB;AAC7E,QAAMiK,aAAaxK,OAAO6C,SAAS,KAAK4H,MAAMC,KAAK1K,MAAX,EAAmB2K;IAAOC,CAAAA,SAASA,SAAS5K,OAAO,CAAD;EAAlD;AACxC,QAAM6K,mBAAmBL,aAAaxK,OAAO,CAAD,IAAMA;AAClD,QAAM8K,oBAAoBvK,eAAeG,OAAOqK,QAAQxK,YAAf,IAA+B;AACxE,MAAIyK,gBAAgBb,gCAAUzJ,QAAQuK,KAAKC,IAAIJ,mBAAmB,CAA5B,CAAT;AAC7B,QAAMK,sBAAsBN,iBAAiBhI,WAAW;AACxD,MAAIsI;AAAqBH,oBAAgBA,cAAc9K;MAAQkL,CAAAA,MAAMA,MAAM7K;IAAlC;AACzC,QAAMK,YAAYoK,cAAcxK;IAAMQ,CAAAA,UACpCA,MAAMqK,YAAN,EAAoBC,WAAWT,iBAAiBQ,YAAjB,CAA/B;EADgB;AAGlB,SAAOzK,cAAcL,eAAeK,YAAYzD;;AAUlD,SAASoO,uCAAiBC,OAAcC,SAAkB;AACxD,QAAM,EAAA,GAAA,EAAKnC,IAAMkC;AACjB,MAAIE,SAAS;AACb,WAASC,IAAI,GAAGC,IAAIH,QAAQ5I,SAAS,GAAG8I,IAAIF,QAAQ5I,QAAQ+I,IAAID,KAAK;AACnE,UAAME,KAAKJ,QAAQE,CAAD,EAAItC;AACtB,UAAMyC,KAAKL,QAAQE,CAAD,EAAIrC;AACtB,UAAMyC,KAAKN,QAAQG,CAAD,EAAIvC;AACtB,UAAM2C,KAAKP,QAAQG,CAAD,EAAItC;AAGtB,UAAM2C,YAAcH,KAAKxC,MAAQ0C,KAAK1C,KAAQD,KAAK0C,KAAKF,OAAOvC,IAAIwC,OAAOE,KAAKF,MAAMD;AACrF,QAAII;AAAWP,eAAS,CAACA;;AAG3B,SAAOA;;AAGT,SAASjK,2CAAqBnD,OAA2BoD,MAAgB;AACvE,MAAI,CAACA;AAAM,WAAO;AAClB,QAAMwK,YAAY;IAAE7C,GAAG/K,MAAMiF;IAAS+F,GAAGhL,MAAMiL;;AAC/C,SAAOgC,uCAAiBW,WAAWxK,IAAZ;;AAGzB,SAAS2B,gCAAa8I,SAAqE;AACzF,SAAQ7N,CAAAA,UAAWA,MAAM8N,gBAAgB,UAAUD,QAAQ7N,KAAD,IAAUnB;;AAGtE,IAAMkP,4CAAOnR;AACb,IAAMoR,4CAAS1P;AACf,IAAM2P,4CAASnP;AACf,IAAMoP,4CAAU7O;AAChB,IAAM8O,4CAAQ/I;AACd,IAAMgJ,4CAAQ7I;AACd,IAAM8I,4CAAO1I;AACb,IAAM2I,4CAAe7G;AACrB,IAAM8G,4CAAapG;AACnB,IAAMqG,4CAAYlG;AAClB,IAAMmG,4CAAgB9F;AACtB,IAAM+F,4CAAY3F;AAClB,IAAM4F,4CAAQzF;AACd,IAAM0F,4CAAMrF;AACZ,IAAMsF,4CAAahF;AACnB,IAAMiF,4CAAaxD;;;AEnyCnB,IAAMyD,2CAAqB;AAG3B,IAAM,CAACC,iDAA2BC,yCAA5B,IAAuDC,yCAC3DH,0CACA;EAACI;CAF4E;AAI/E,IAAMC,qCAAeD,0CAAe;AAYpC,IAAM,CAACE,4CAAsBC,4CAAvB,IACJN,gDAAoDD,wCAA3B;AAW3B,IAAMQ,4CAA6CC,CAAAA,UAA0C;AAC3F,QAAM,EAAA,qBAAA,UAAA,KAIJC,MAAMC,UAJF,aAAA,cAAA,QAOI,KAARC,IACEH;AACJ,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,QAAMC,iBAAaC,eAAAA,QAAgC,IAAhC;AACnB,QAAM,CAACN,OAAO,OAAOO,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMR;IACNS,aAAaC;IACbC,UAAUC;GAHwC;AAMpD,aACE,eAAAC,eAAC,4CADH;IAEI,OAAOV;IACP,WAAWW,0CAAK;IAChB;IACA,WAAWA,0CAAK;IAChB;IACA,cAAcR;IACd,kBAAcD,eAAAA;MAAkB,MAAMC;QAASS,CAAAA,aAAa,CAACA;MAAhB;MAA2B;QAACT;;IAA3D;IACd;SAEA,eAAAO,eAAC,2CAAD,SAAA,CAAA,GAAwBX,WAV1B;IAUqC;IAAY,cAAcI;IAAS;IAAU;GAAhF,GACGU,QADH,CAVF;;AAiBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAMrB,IAAMC,gDAAsBb,eAAAA,YAC1B,CAACP,OAA8CqB,iBAAiB;AAC9D,QAAM,EAAA,qBAAA,WAAkC,OAAO,GAAGC,aAAH,IAAoBtB;AACnE,QAAMuB,UAAUzB,6CAAuBqB,oCAAcd,mBAAf;AACtC,QAAMD,YAAYR,mCAAaS,mBAAD;AAC9B,aACE,eAAAU,eAAC,2CADH,SAAA;IACwB,SAAA;KAAYX,SAAlC,OACE,eAAAW,eAAC,0CAAU,QADb,SAAA;IAEI,MAAK;IACL,IAAIQ,QAAQC;IACZ,iBAAc;IACd,iBAAeD,QAAQtB;IACvB,iBAAesB,QAAQtB,OAAOsB,QAAQE,YAAYC;IAClD,cAAYH,QAAQtB,OAAO,SAAS;IACpC,iBAAe0B,WAAW,KAAKD;IAC/B;KACIJ,cATN;IAUE,KAAKM,0CAAYP,cAAcE,QAAQjB,UAAvB;IAChB,eAAeuB,0CAAqB7B,MAAM8B,eAAgBC,CAAAA,UAAU;AAGlE,UAAI,CAACJ,YAAYI,MAAMC,WAAW,KAAKD,MAAME,YAAY,OAAO;AAC9DV,gBAAQW,aAAR;AAGA,YAAI,CAACX,QAAQtB;AAAM8B,gBAAMI,eAAN;;KAPY;IAUnC,WAAWN,0CAAqB7B,MAAMoC,WAAYL,CAAAA,UAAU;AAC1D,UAAIJ;AAAU;AACd,UAAI;QAAC;QAAS;QAAKU,SAASN,MAAMO,GAA9B;AAAoCf,gBAAQW,aAAR;AACxC,UAAIH,MAAMO,QAAQ;AAAaf,gBAAQT,aAAa,IAArB;AAG/B,UAAI;QAAC;QAAS;QAAK;QAAauB,SAASN,MAAMO,GAA3C;AAAiDP,cAAMI,eAAN;KANxB;GArBjC,CAAA,CADF;CANsB;AA0C5B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMI,oCAAc;AAKpB,IAAMC,4CACJxC,CAAAA,UACG;AACH,QAAM,EAAA,qBAAuB,GAAGyC,YAAH,IAAmBzC;AAChD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAA0BX,WAAeqC,WAAzC,CAAA;;AAGT,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAMrB,IAAMC,gDAAsBpC,eAAAA,YAC1B,CAACP,OAA8CqB,iBAAiB;AAC9D,QAAM,EAAA,qBAAuB,GAAGuB,aAAH,IAAoB5C;AACjD,QAAMuB,UAAUzB,6CAAuB4C,oCAAcrC,mBAAf;AACtC,QAAMD,YAAYR,mCAAaS,mBAAD;AAC9B,QAAMwC,8BAA0BtC,eAAAA,QAAa,KAAb;AAEhC,aACE,eAAAQ,eAAC,2CADH,SAAA;IAEI,IAAIQ,QAAQE;IACZ,mBAAiBF,QAAQC;KACrBpB,WACAwC,cAJN;IAKE,KAAKvB;IACL,kBAAkBQ,0CAAqB7B,MAAM8C,kBAAmBf,CAAAA,UAAU;AAAA,UAAA;AACxE,UAAI,CAACc,wBAAwBE;AAAS,SAAA,wBAAAxB,QAAQjB,WAAWyC,aAAnB,QAAA,0BAAA,UAAA,sBAA4BC,MAA5B;AACtCH,8BAAwBE,UAAU;AAElChB,YAAMI,eAAN;KAJoC;IAMtC,mBAAmBN,0CAAqB7B,MAAMiD,mBAAoBlB,CAAAA,UAAU;AAC1E,YAAMmB,gBAAgBnB,MAAMoB,OAAOD;AACnC,YAAME,gBAAgBF,cAAclB,WAAW,KAAKkB,cAAcjB,YAAY;AAC9E,YAAMoB,eAAeH,cAAclB,WAAW,KAAKoB;AACnD,UAAI,CAAC7B,QAAQpB,SAASkD;AAAcR,gCAAwBE,UAAU;KAJjC;IAMvC,OAAO;MACL,GAAG/C,MAAMsD;MAGP,kDACE;MACF,iDAAiD;MACjD,kDACE;MACF,uCAAuC;MACvC,wCAAwC;;GA5B9C,CAAA;CARsB;AA4C5B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,mCAAa;AAMnB,IAAMC,gDAAoBjD,eAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAGoC,WAAH,IAAkBzD;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAeqD,YAA/C;IAA2D,KAAKpC;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMqC,mCAAa;AAMnB,IAAMC,gDAAoBpD,eAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAGuC,WAAH,IAAkB5D;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAewD,YAA/C;IAA2D,KAAKvC;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMwC,kCAAY;AAMlB,IAAMC,gDAAmBvD,eAAAA,YACvB,CAACP,OAA2CqB,iBAAiB;AAC3D,QAAM,EAAA,qBAAuB,GAAG0C,UAAH,IAAiB/D;AAC9C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAwBX,WAAe2D,WAA9C;IAAyD,KAAK1C;GAAvD,CAAA;CAJc;AAQzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM2C,2CAAqB;AAM3B,IAAMC,gDAA2B1D,eAAAA,YAG/B,CAACP,OAAmDqB,iBAAiB;AACrE,QAAM,EAAA,qBAAuB,GAAG6C,kBAAH,IAAyBlE;AACtD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAgCX,WAAe8D,mBAAtD;IAAyE,KAAK7C;GAAvE,CAAA;CANwB;AASjC,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM8C,yCAAmB;AAMzB,IAAMC,gDAAyB7D,eAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGgD,gBAAH,IAAuBrE;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAA8BX,WAAeiE,iBAApD;IAAqE,KAAKhD;GAAnE,CAAA;CANsB;AAS/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiD,wCAAkB;AAMxB,IAAMC,gDAAwBhE,eAAAA,YAG5B,CAACP,OAAgDqB,iBAAiB;AAClE,QAAM,EAAA,qBAAuB,GAAGmD,eAAH,IAAsBxE;AACnD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAA6BX,WAAeoE,gBAAnD;IAAmE,KAAKnD;GAAjE,CAAA;CANqB;AAS9B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMoD,uCAAiB;AAMvB,IAAMC,gDAA4BnE,eAAAA,YAGhC,CAACP,OAAoDqB,iBAAiB;AACtE,QAAM,EAAA,qBAAuB,GAAGsD,mBAAH,IAA0B3E;AACvD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAiCX,WAAeuE,oBAAvD;IAA2E,KAAKtD;GAAzE,CAAA;CANyB;AASlC,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMuD,uCAAiB;AAMvB,IAAMC,gDAAwBtE,eAAAA,YAG5B,CAACP,OAAgDqB,iBAAiB;AAClE,QAAM,EAAA,qBAAuB,GAAGyD,eAAH,IAAsB9E;AACnD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAA6BX,WAAe0E,gBAAnD;IAAmE,KAAKzD;GAAjE,CAAA;CANqB;AAS9B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAM0D,mCAAa;AAMnB,IAAMC,gDAAoBzE,eAAAA,YACxB,CAACP,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,qBAAuB,GAAG4D,WAAH,IAAkBjF;AAC/C,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAAyBX,WAAe6E,YAA/C;IAA2D,KAAK5D;GAAzD,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAaA,IAAM6D,4CACJlF,CAAAA,UACG;AACH,QAAM,EAAA,qBAAA,UAAiCC,MAAMC,UAAvC,cAAA,YAA+DU,IAAgBZ;AACrF,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,QAAM,CAACJ,OAAO,OAAOO,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMR;IACNS,aAAaC;IACbC,UAAUC;GAHwC;AAMpD,aACE,eAAAC,eAAC,2CAAD,SAAA,CAAA,GAAuBX,WADzB;IACoC;IAAY,cAAcI;GAA5D,GACGU,QADH;;AAUJ,IAAMiE,yCAAmB;AAMzB,IAAMC,gDAAyB7E,eAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGgE,gBAAH,IAAuBrF;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAC9B,aAAO,eAAAU,eAAC,2CAAD,SAAA,CAAA,GAA8BX,WAAeiF,iBAApD;IAAqE,KAAKhE;GAAnE,CAAA;CANsB;AAS/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMiE,yCAAmB;AAMzB,IAAMC,gDAAyBhF,eAAAA,YAG7B,CAACP,OAAiDqB,iBAAiB;AACnE,QAAM,EAAA,qBAAuB,GAAGmE,gBAAH,IAAuBxF;AACpD,QAAMI,YAAYR,mCAAaS,mBAAD;AAE9B,aACE,eAAAU,eAAC,2CAAD,SAAA,CAAA,GACMX,WACAoF,iBAHR;IAII,KAAKnE;IACL,OAAO;MACL,GAAGrB,MAAMsD;MAGP,kDAAkD;MAClD,iDAAiD;MACjD,kDAAkD;MAClD,uCAAuC;MACvC,wCAAwC;;GAZ9C,CAAA;CAR2B;AA2B/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,IAAMmC,4CAAO1F;AACb,IAAM2F,4CAAUtE;AAChB,IAAMuE,4CAASnD;AACf,IAAMoD,4CAAUjD;AAChB,IAAMkD,4CAAQrC;AACd,IAAMsC,4CAAQnC;AACd,IAAMoC,4CAAOjC;AACb,IAAMkC,4CAAe/B;AACrB,IAAMgC,4CAAa7B;AACnB,IAAM8B,4CAAY3B;AAClB,IAAM4B,4CAAgBzB;AACtB,IAAM0B,4CAAYvB;AAClB,IAAMwB,4CAAQrB;AACd,IAAMsB,4CAAMpB;AACZ,IAAMqB,4CAAanB;AACnB,IAAMoB,4CAAajB;", "names": ["composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "handleEvent", "event", "defaultPrevented", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createContext", "rootComponentName", "defaultContext", "BaseContext", "React", "index", "length", "Provider", "props", "context", "Context", "scope", "value", "Object", "values", "$3bkAK$createElement", "children", "useContext", "consumerName", "undefined", "Error", "displayName", "createScope", "scopeContexts", "map", "useScope", "contexts", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "useComposedScopes", "overrideScopes", "nextScopes", "reduce", "scopeProps", "currentScope", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React", "current", "args", "useControllableState", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "isControlled", "prop", "undefined", "value", "handleChange", "useCallbackRef", "setValue", "React", "nextValue", "setter", "uncontrolledState", "defaultProp", "prevValueRef", "current", "NODES", "Primitive", "reduce", "primitive", "node", "Node", "React", "props", "forwardedRef", "primitiveProps", "Comp", "<PERSON><PERSON><PERSON><PERSON>", "Slot", "window", "Symbol", "for", "$4q5Fq$createElement", "displayName", "dispatchDiscreteCustomEvent", "target", "event", "ReactDOM", "dispatchEvent", "createCollection", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "createContextScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "Map", "CollectionProvider", "props", "children", "ref", "React", "useRef", "$6vYhU$react", "COLLECTION_SLOT_NAME", "CollectionSlot", "forwardRef", "forwardedRef", "context", "scope", "composedRefs", "useComposedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "useCollection", "getItems", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "orderedItems", "sort", "a", "b", "indexOf", "Provider", "Slot", "ItemSlot", "DirectionContext", "React", "undefined", "useDirection", "localDir", "globalDir", "React", "DirectionContext", "useEscapeKeydown", "onEscapeKeyDownProp", "ownerDocument", "globalThis", "document", "onEscapeKeyDown", "useCallbackRef", "React", "handleKeyDown", "event", "key", "addEventListener", "removeEventListener", "DISMISSABLE_LAYER_NAME", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "originalBodyPointerEvents", "DismissableLayerContext", "React", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "props", "forwardedRef", "layerProps", "context", "node", "setNode", "ownerDocument", "globalThis", "document", "force", "composedRefs", "useComposedRefs", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "index", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "usePointerDownOutside", "event", "target", "isPointerDownOnBranch", "some", "branch", "contains", "onPointerDownOutside", "onInteractOutside", "defaultPrevented", "on<PERSON><PERSON><PERSON>", "focusOutside", "useFocusOutside", "isFocusInBranch", "onFocusOutside", "useEscapeKeydown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onEscapeKeyDown", "preventDefault", "disableOutsidePointerEvents", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "addEventListener", "removeEventListener", "$kqwpH$createElement", "undefined", "composeEventHandlers", "onFocusCapture", "onBlurCapture", "onPointerDownCapture", "BRANCH_NAME", "DismissableLayerBranch", "ref", "current", "handlePointerDownOutside", "useCallbackRef", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent", "handleAndDispatchCustomEvent", "eventDetail", "discrete", "originalEvent", "pointerType", "once", "timerId", "window", "setTimeout", "clearTimeout", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "CustomEvent", "dispatchEvent", "name", "handler", "detail", "bubbles", "cancelable", "dispatchDiscreteCustomEvent", "count", "useFocusGuards", "React", "edgeGuards", "document", "querySelectorAll", "body", "insertAdjacentElement", "createFocusGuard", "count", "for<PERSON>ach", "node", "remove", "element", "createElement", "setAttribute", "tabIndex", "style", "cssText", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FOCUS_SCOPE_NAME", "FocusScope", "React", "props", "forwardedRef", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useCallbackRef", "lastFocusedElementRef", "composedRefs", "useComposedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "trapped", "handleFocusIn", "event", "target", "contains", "focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "document", "activeElement", "body", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "focusScopesStack", "add", "previouslyFocusedElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "focusFirst", "removeLinks", "getTabbableCandidates", "setTimeout", "unmountEvent", "remove", "handleKeyDown", "loop", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "currentTarget", "first", "last", "getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "$45QHv$createElement", "candidates", "candidate", "findVisible", "reverse", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "tabIndex", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "isSelectableInput", "HTMLInputElement", "preventScroll", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "unshift", "array", "item", "updatedArray", "index", "indexOf", "splice", "items", "filter", "useLayoutEffect", "Boolean", "globalThis", "document", "React", "useReactId", "React", "toString", "undefined", "count", "useId", "deterministicId", "id", "setId", "useState", "useLayoutEffect", "reactId", "String", "NAME", "Arrow", "React", "props", "forwardedRef", "arrowProps", "$jbnEx$createElement", "<PERSON><PERSON><PERSON><PERSON>", "children", "Root", "useSize", "element", "size", "setSize", "React", "undefined", "useLayoutEffect", "width", "offsetWidth", "height", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve", "POPPER_NAME", "createPopperContext", "createPopperScope", "createContextScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "<PERSON><PERSON>", "props", "children", "anchor", "setAnchor", "React", "$kY93V$createElement", "__scope<PERSON>opper", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "forwardedRef", "anchorProps", "context", "ref", "composedRefs", "useComposedRefs", "onAnchorChange", "virtualRef", "current", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collisionPadding", "collisionPaddingProp", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "arrow", "setArrow", "arrowSize", "useSize", "arrow<PERSON>idth", "width", "arrowHeight", "height", "desiredPlacement", "side", "align", "top", "right", "bottom", "left", "boundary", "Array", "isArray", "collisionBoundary", "hasExplicitBoundaries", "length", "detectOverflowOptions", "padding", "filter", "isNotNull", "altBoundary", "middlewareData", "useFloating", "strategy", "placement", "whileElementsMounted", "args", "cleanup", "autoUpdate", "animationFrame", "updatePositionStrategy", "elements", "reference", "middleware", "offset", "mainAxis", "sideOffset", "alignmentAxis", "alignOffset", "avoidCollisions", "shift", "crossAxis", "limiter", "sticky", "limitShift", "undefined", "flip", "size", "apply", "availableHeight", "anchorWidth", "anchorHeight", "rects", "contentStyle", "floating", "style", "setProperty", "availableWidth", "floatingUIarrow", "element", "arrowPadding", "transform<PERSON><PERSON>in", "hideWhenDetached", "hide", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "useCallbackRef", "onPlaced", "useLayoutEffect", "isPositioned", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "refs", "setFloating", "floatingStyles", "transform", "min<PERSON><PERSON><PERSON>", "join", "dir", "animation", "opacity", "referenceHidden", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "arrowProps", "contentContext", "baseSide", "onArrowChange", "position", "visibility", "shouldHideArrow", "display", "value", "options", "name", "fn", "data", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split", "Root", "<PERSON><PERSON>", "Content", "Arrow", "PORTAL_NAME", "Portal", "React", "props", "forwardedRef", "globalThis", "document", "body", "portalProps", "container", "ReactDOM", "createPortal", "$7SXl2$createElement", "useStateMachine", "initialState", "machine", "React", "state", "event", "nextState", "Presence", "props", "children", "presence", "usePresence", "present", "child", "isPresent", "only", "ref", "useComposedRefs", "forceMount", "displayName", "node", "setNode", "stylesRef", "prevPresentRef", "prevAnimationNameRef", "send", "mounted", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "currentAnimationName", "getAnimationName", "current", "useLayoutEffect", "styles", "wasPresent", "hasPresentChanged", "prevAnimationName", "display", "isAnimating", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "target", "ReactDOM", "handleAnimationStart", "addEventListener", "removeEventListener", "getComputedStyle", "ENTRY_FOCUS", "EVENT_OPTIONS", "bubbles", "cancelable", "GROUP_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createRovingFocusGroupContext", "createRovingFocusGroupScope", "createContextScope", "RovingFocus<PERSON><PERSON>ider", "useRovingFocusContext", "RovingFocusGroup", "React", "props", "forwardedRef", "$98Iye$createElement", "__scopeRovingFocusGroup", "RovingFocusGroupImpl", "currentTabStopId", "currentTabStopIdProp", "groupProps", "ref", "composedRefs", "useComposedRefs", "direction", "useDirection", "dir", "setCurrentTabStopId", "useControllableState", "prop", "defaultProp", "defaultCurrentTabStopId", "onChange", "onCurrentTabStopIdChange", "isTabbingBackOut", "setIsTabbingBackOut", "handleEntryFocus", "useCallbackRef", "onEntryFocus", "getItems", "isClickFocusRef", "focusableItemsCount", "setFocusableItemsCount", "node", "current", "addEventListener", "removeEventListener", "tabStopId", "prevCount", "orientation", "outline", "style", "composeEventHandlers", "onMouseDown", "onFocus", "event", "isKeyboardFocus", "target", "currentTarget", "entryFocusEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "items", "filter", "item", "focusable", "activeItem", "find", "active", "currentItem", "id", "candidate<PERSON><PERSON>s", "Boolean", "candidateNodes", "map", "focusFirst", "onBlur", "ITEM_NAME", "RovingFocusGroupItem", "itemProps", "autoId", "useId", "context", "isCurrentTabStop", "onFocusableItemRemove", "onFocusableItemAdd", "preventDefault", "onItemFocus", "onKeyDown", "key", "shift<PERSON>ey", "onItemShiftTab", "focusIntent", "getFocusIntent", "undefined", "reverse", "currentIndex", "indexOf", "loop", "wrapArray", "slice", "setTimeout", "MAP_KEY_TO_FOCUS_INTENT", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "PageUp", "Home", "PageDown", "End", "getDirectionAwareKey", "includes", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "document", "activeElement", "candidate", "focus", "array", "startIndex", "_", "index", "length", "Root", "<PERSON><PERSON>", "React", "React", "SELECTION_KEYS", "FIRST_KEYS", "LAST_KEYS", "FIRST_LAST_KEYS", "SUB_OPEN_KEYS", "ltr", "rtl", "SUB_CLOSE_KEYS", "MENU_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createMenuContext", "createMenuScope", "createContextScope", "createPopperScope", "createRovingFocusGroupScope", "usePopperScope", "useRovingFocusGroupScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContext", "MenuRootProvider", "useMenuRootContext", "<PERSON><PERSON>", "props", "modal", "popperScope", "__scopeMenu", "content", "<PERSON><PERSON><PERSON><PERSON>", "React", "isUsingKeyboardRef", "handleOpenChange", "useCallbackRef", "onOpenChange", "direction", "useDirection", "dir", "handleKeyDown", "current", "document", "addEventListener", "handlePointer", "capture", "once", "removeEventListener", "$epM9y$createElement", "children", "ANCHOR_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedRef", "anchorProps", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "undefined", "<PERSON>uPort<PERSON>", "container", "context", "open", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "contentProps", "rootContext", "MenuRootContentModal", "ref", "composedRefs", "useComposedRefs", "hideOthers", "composeEventHandlers", "onFocusOutside", "event", "preventDefault", "checkForDefaultPrevented", "MenuRootContentNonModal", "MenuContentImpl", "rovingFocusGroupScope", "getItems", "currentItemId", "setCurrentItemId", "contentRef", "onContentChange", "timerRef", "searchRef", "pointerGraceTimerRef", "pointerGraceIntentRef", "pointerDirRef", "lastPointerXRef", "ScrollLockWrapper", "disableOutsideScroll", "RemoveScroll", "scrollLockWrapperProps", "as", "Slot", "allowPinchZoom", "handleTypeaheadSearch", "key", "search", "items", "filter", "item", "disabled", "currentItem", "activeElement", "currentMatch", "find", "textValue", "values", "map", "nextMatch", "getNextMatch", "newItem", "updateSearch", "value", "window", "clearTimeout", "setTimeout", "focus", "useFocusGuards", "isPointerMovingToSubmenu", "isMovingTowards", "side", "isPointerInGraceArea", "area", "intent", "trapFocus", "onOpenAutoFocus", "onCloseAutoFocus", "onEntryFocus", "getOpenState", "outline", "style", "onKeyDown", "target", "isKeyDownInside", "closest", "currentTarget", "isModifierKey", "ctrl<PERSON>ey", "altKey", "metaKey", "isCharacterKey", "length", "includes", "candidateNodes", "reverse", "focusFirst", "onBlur", "contains", "onPointerMove", "whenMouse", "pointer<PERSON><PERSON>as<PERSON><PERSON><PERSON>", "clientX", "newDir", "GROUP_NAME", "MenuGroup", "groupProps", "LABEL_NAME", "<PERSON>u<PERSON><PERSON><PERSON>", "labelProps", "ITEM_NAME", "ITEM_SELECT", "MenuItem", "itemProps", "contentContext", "isPointerDownRef", "handleSelect", "menuItem", "itemSelectEvent", "CustomEvent", "bubbles", "cancelable", "onSelect", "dispatchDiscreteCustomEvent", "defaultPrevented", "onClose", "onClick", "onPointerDown", "onPointerUp", "click", "isTypingAhead", "MenuItemImpl", "isFocused", "setIsFocused", "textContent", "setTextContent", "trim", "onItemLeave", "onItemEnter", "onPointerLeave", "onFocus", "CHECKBOX_ITEM_NAME", "MenuCheckboxItem", "checkboxItemProps", "isIndeterminate", "checked", "getCheckedState", "onCheckedChange", "RADIO_GROUP_NAME", "RadioGroupProvider", "useRadioGroupContext", "onValueChange", "MenuRadioGroup", "handleValueChange", "RADIO_ITEM_NAME", "MenuRadioItem", "radioItemProps", "ITEM_INDICATOR_NAME", "ItemIndicatorProvider", "useItemIndicatorContext", "MenuItemIndicator", "itemIndicatorProps", "indicatorContext", "SEPARATOR_NAME", "MenuSeparator", "separatorProps", "ARROW_NAME", "MenuArrow", "arrowProps", "SUB_NAME", "MenuSub<PERSON><PERSON><PERSON>", "useMenuSubContext", "MenuSub", "parentMenuContext", "trigger", "setTrigger", "useId", "SUB_TRIGGER_NAME", "MenuSubTrigger", "subContext", "openTimerRef", "onPointerGraceIntentChange", "scope", "clearOpenTimer", "pointerGraceTimer", "triggerId", "contentId", "composeRefs", "onTriggerChange", "contentRect", "getBoundingClientRect", "dataset", "rightSide", "bleed", "contentNearEdge", "contentFarEdge", "x", "y", "clientY", "top", "bottom", "onTriggerLeave", "SUB_CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subContentProps", "onEscapeKeyDown", "isClose<PERSON>ey", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "candidate", "wrapArray", "array", "startIndex", "_", "index", "isRepeated", "Array", "from", "every", "char", "normalizedSearch", "currentMatchIndex", "indexOf", "wrappedValues", "Math", "max", "excludeCurrentMatch", "v", "toLowerCase", "startsWith", "isPointInPolygon", "point", "polygon", "inside", "i", "j", "xi", "yi", "xj", "yj", "intersect", "cursorPos", "handler", "pointerType", "Root", "<PERSON><PERSON>", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent", "DROPDOWN_MENU_NAME", "createDropdownMenuContext", "createDropdownMenuScope", "createContextScope", "createMenuScope", "useMenuScope", "DropdownMenuProvider", "useDropdownMenuContext", "DropdownMenu", "props", "open", "openProp", "modal", "menuScope", "__scopeDropdownMenu", "triggerRef", "React", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "defaultOpen", "onChange", "onOpenChange", "$9kmUS$createElement", "useId", "prevOpen", "children", "TRIGGER_NAME", "DropdownMenuTrigger", "forwardedRef", "triggerProps", "context", "triggerId", "contentId", "undefined", "disabled", "composeRefs", "composeEventHandlers", "onPointerDown", "event", "button", "ctrl<PERSON>ey", "onOpenToggle", "preventDefault", "onKeyDown", "includes", "key", "PORTAL_NAME", "DropdownMenuPortal", "portalProps", "CONTENT_NAME", "DropdownMenuContent", "contentProps", "hasInteractedOutsideRef", "onCloseAutoFocus", "current", "focus", "onInteractOutside", "originalEvent", "detail", "ctrlLeftClick", "isRightClick", "style", "GROUP_NAME", "DropdownMenuGroup", "groupProps", "LABEL_NAME", "DropdownMenuLabel", "labelProps", "ITEM_NAME", "DropdownMenuItem", "itemProps", "CHECKBOX_ITEM_NAME", "DropdownMenuCheckboxItem", "checkboxItemProps", "RADIO_GROUP_NAME", "DropdownMenuRadioGroup", "radioGroupProps", "RADIO_ITEM_NAME", "DropdownMenuRadioItem", "radioItemProps", "INDICATOR_NAME", "DropdownMenuItemIndicator", "itemIndicatorProps", "SEPARATOR_NAME", "DropdownMenuSeparator", "separatorProps", "ARROW_NAME", "DropdownMenuArrow", "arrowProps", "DropdownMenuSub", "SUB_TRIGGER_NAME", "DropdownMenuSubTrigger", "subTriggerProps", "SUB_CONTENT_NAME", "DropdownMenuSubContent", "subContentProps", "Root", "<PERSON><PERSON>", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}