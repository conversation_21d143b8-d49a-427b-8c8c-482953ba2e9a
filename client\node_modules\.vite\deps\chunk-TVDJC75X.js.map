{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/extends.js", "../../@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/index.ts", "../../@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/composeRefs.tsx", "../../@radix-ui/react-slot/dist/packages/react/slot/src/index.ts", "../../@radix-ui/react-slot/dist/packages/react/slot/src/Slot.tsx"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}", "export { composeRefs, useComposedRefs } from './composeRefs';\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './Slot';\nexport type { SlotProps } from './Slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n"], "mappings": ";;;;;;;;AAAe,SAAR,WAA4B;AACjC,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;;;;;;;AELA,SAASA,6BAAUC,KAAqBC,OAAU;AAChD,MAAI,OAAOD,QAAQ;AACjBA,QAAIC,KAAD;WACMD,QAAQ,QAAQA,QAAQE;AAChCF,QAAkCG,UAAUF;;AAQjD,SAASG,6CAAkBC,MAAwB;AACjD,SAAQC,CAAAA,SAAYD,KAAKE;IAASP,CAAAA,QAAQD,6BAAOC,KAAKM,IAAN;EAA5B;;AAOtB,SAASE,6CAAsBH,MAAwB;AAErD,aAAOI,aAAAA,aAAkBL,0CAAW,GAAIC,IAAJ,GAAWA,IAAxC;;;;AEnBT,IAAMK,gDAAOC,cAAAA,YAAyC,CAACC,OAAOC,iBAAiB;AAC7E,QAAM,EAAA,UAAY,GAAGC,UAAH,IAAiBF;AACnC,QAAMG,gBAAgBJ,cAAAA,SAAeK,QAAQC,QAAvB;AACtB,QAAMC,YAAYH,cAAcI,KAAKC,iCAAnB;AAElB,MAAIF,WAAW;AAEb,UAAMG,aAAaH,UAAUN,MAAMK;AAEnC,UAAMK,cAAcP,cAAcQ,IAAKC,CAAAA,UAAU;AAC/C,UAAIA,UAAUN,WAAW;AAGvB,YAAIP,cAAAA,SAAec,MAAMJ,UAArB,IAAmC;AAAG,iBAAOV,cAAAA,SAAee,KAAK,IAApB;AACjD,mBAAOf,cAAAA,gBAAqBU,UAArB,IACFA,WAAWT,MAAMK,WAClB;;AAEJ,eAAOO;KATS;AAapB,eACE,cAAAG,eAAC,iCAAD,SAAA,CAAA,GAAeb,WADjB;MAC4B,KAAKD;KAA/B,OACGF,cAAAA,gBAAqBU,UAArB,QACGV,cAAAA,cAAmBU,YAAYO,QAAWN,WAA1C,IACA,IAHN;;AAQJ,aACE,cAAAK,eAAC,iCAAD,SAAA,CAAA,GAAeb,WADjB;IAC4B,KAAKD;GAA/B,GACGI,QADH;CAhCS;AAsCbP,0CAAKmB,cAAc;AAUnB,IAAMC,sCAAYnB,cAAAA,YAAsC,CAACC,OAAOC,iBAAiB;AAC/E,QAAM,EAAA,UAAY,GAAGC,UAAH,IAAiBF;AAEnC,UAAID,cAAAA,gBAAqBM,QAArB;AACF,eAAON,cAAAA,cAAmBM,UAAU;MAClC,GAAGc,iCAAWjB,WAAWG,SAASL,KAArB;MACboB,KAAKnB,eAAeoB,0CAAYpB,cAAeI,SAAiBe,GAAjC,IAAyCf,SAAiBe;KAFpF;AAMT,SAAOrB,cAAAA,SAAec,MAAMR,QAArB,IAAiC,IAAIN,cAAAA,SAAee,KAAK,IAApB,IAA4B;CAVxD;AAalBI,gCAAUD,cAAc;AAMxB,IAAMK,4CAAY,CAAC,EAAA,SAAEjB,MAA8C;AACjE,aAAO,cAAAU,eAAA,cAAAQ,UAAA,MAAGlB,QAAH;;AAOT,SAASG,kCAAYI,OAAqD;AACxE,aAAOb,cAAAA,gBAAqBa,KAArB,KAA+BA,MAAMY,SAASF;;AAGvD,SAASH,iCAAWjB,WAAqBuB,YAAsB;AAE7D,QAAMC,gBAAgB;IAAE,GAAGD;;AAE3B,aAAWE,YAAYF,YAAY;AACjC,UAAMG,gBAAgB1B,UAAUyB,QAAD;AAC/B,UAAME,iBAAiBJ,WAAWE,QAAD;AAEjC,UAAMG,YAAY,WAAWC,KAAKJ,QAAhB;AAClB,QAAIG,WAAW;AAEb,UAAIF,iBAAiBC;AACnBH,sBAAcC,QAAD,IAAa,IAAIK,SAAoB;AAChDH,yBAAc,GAAIG,IAAJ;AACdJ,wBAAa,GAAII,IAAJ;;eAIRJ;AACPF,sBAAcC,QAAD,IAAaC;eAIrBD,aAAa;AACpBD,oBAAcC,QAAD,IAAa;QAAE,GAAGC;QAAe,GAAGC;;aACxCF,aAAa;AACtBD,oBAAcC,QAAD,IAAa;QAACC;QAAeC;QAAgBI,OAAOC,OAAvC,EAAgDC,KAAK,GAArD;;AAI9B,SAAO;IAAE,GAAGjC;IAAW,GAAGwB;;;AAG5B,IAAMU,4CAAOtC;", "names": ["setRef", "ref", "value", "undefined", "current", "composeRefs", "refs", "node", "for<PERSON>ach", "useComposedRefs", "React", "Slot", "React", "props", "forwardedRef", "slotProps", "childrenA<PERSON>y", "toArray", "children", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "count", "only", "$9IrjX$createElement", "undefined", "displayName", "SlotClone", "mergeProps", "ref", "composeRefs", "Slottable", "$9IrjX$Fragment", "type", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "<PERSON><PERSON><PERSON><PERSON>", "test", "args", "filter", "Boolean", "join", "Root"]}