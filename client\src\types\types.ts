export interface GradientCardProps {
  gradient: { name: string; colors: string[] };
  isFavorite: boolean;
  onFavoriteToggle: (name: string) => void;
}

// Gradient Builder Types
export type GradientType = 'linear' | 'radial' | 'conic';
export type ColorFormat = 'HEX' | 'RGB' | 'HSL';
export type RadialShape = 'circle' | 'ellipse';
export type RadialPosition = 'center' | 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

export interface ColorStop {
  id: string;
  color: string;
  alpha: number;
  position: number; // 0-100
  format: ColorFormat;
}

export interface LinearGradientConfig {
  type: 'linear';
  angle: number; // 0-360 degrees
}

export interface RadialGradientConfig {
  type: 'radial';
  shape: RadialShape;
  position: RadialPosition;
  centerX?: number; // 0-100 for custom position
  centerY?: number; // 0-100 for custom position
}

export interface ConicGradientConfig {
  type: 'conic';
  startAngle: number; // 0-360 degrees
  centerX: number; // 0-100
  centerY: number; // 0-100
}

export type GradientConfig = LinearGradientConfig | RadialGradientConfig | ConicGradientConfig;

export interface GradientState {
  id: string;
  name: string;
  colorStops: ColorStop[];
  config: GradientConfig;
  createdAt: Date;
  updatedAt: Date;
}

export interface ColorConversion {
  hex: string;
  rgb: { r: number; g: number; b: number; a?: number };
  hsl: { h: number; s: number; l: number; a?: number };
}

// Component Props
export interface GradientBuilderProps {
  initialGradient?: Partial<GradientState>;
  onSave?: (gradient: GradientState) => void;
  onExport?: (gradient: GradientState, format: string) => void;
}

export interface ColorStopManagerProps {
  colorStops: ColorStop[];
  onColorStopsChange: (colorStops: ColorStop[]) => void;
  maxStops?: number;
  minStops?: number;
}

export interface GradientTypeSelectorProps {
  gradientType: GradientType;
  onGradientTypeChange: (type: GradientType) => void;
  showIcons?: boolean;
}

export interface GradientControlsProps {
  config: GradientConfig;
  onConfigChange: (config: GradientConfig) => void;
}

export interface GradientPreviewProps {
  gradient: GradientState;
  backgroundPattern?: 'transparent' | 'white' | 'black';
  className?: string;
}

export interface CodeOutputProps {
  gradient: GradientState;
  activeFormat: 'css' | 'tailwind' | 'sass' | 'bootstrap';
  onFormatChange: (format: 'css' | 'tailwind' | 'sass' | 'bootstrap') => void;
  onCopy?: (code: string, format: string) => void;
}

// Utility Types
export interface ColorValidationResult {
  isValid: boolean;
  error?: string;
  normalizedColor?: string;
}

export interface GradientCodeOutput {
  css: string;
  tailwind: string;
  sass: string;
  bootstrap: string;
}
